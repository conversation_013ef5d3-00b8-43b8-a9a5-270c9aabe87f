#!/bin/bash

echo "=== DevInsight 插件系统 HTTP API 测试 ==="

BASE_URL="http://localhost:8080/api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local endpoint="$1"
    local description="$2"
    local method="${3:-GET}"
    local data="$4"
    
    echo -e "\n${YELLOW}测试: $description${NC}"
    echo "请求: $method $BASE_URL$endpoint"
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$BASE_URL$endpoint")
    fi
    
    http_code=$(echo "$response" | tail -n1 | cut -d: -f2)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ 成功 (HTTP $http_code)${NC}"
        echo "$body" | jq . 2>/dev/null || echo "$body"
    else
        echo -e "${RED}✗ 失败 (HTTP $http_code)${NC}"
        echo "$body"
    fi
}

# 1. 测试插件列表
test_api "/plugins" "获取所有插件列表"

# 2. 测试采集器插件列表
test_api "/plugins/collectors" "获取采集器插件列表"

# 3. 测试插件健康检查
test_api "/plugins/health-check" "检查所有插件健康状态" "POST"

# 4. 测试获取特定插件信息
test_api "/plugins/system-collector/1.0.0" "获取system-collector插件信息"

# 5. 测试获取插件指标
test_api "/plugins/system-collector/1.0.0/metrics" "获取system-collector插件指标"

# 6. 测试插件健康检查
test_api "/plugins/system-collector/1.0.0/health" "检查system-collector插件健康状态"

# 7. 测试获取支持的指标
test_api "/plugins/collectors/supported-metrics/linux" "获取Linux设备支持的指标"

# 8. 测试指标分析
test_api "/plugins/analyze" "分析指标数据" "POST" '[
    {
        "metric_key": "cpu_usage",
        "value": 85.5,
        "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
        "device_id": "test-device"
    },
    {
        "metric_key": "memory_usage", 
        "value": 78.2,
        "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
        "device_id": "test-device"
    }
]'

# 9. 测试异常检测
test_api "/plugins/detect-anomalies" "检测异常" "POST" '[
    {
        "metric_key": "cpu_usage",
        "value": 95.8,
        "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
        "device_id": "test-device"
    },
    {
        "metric_key": "memory_usage",
        "value": 89.3,
        "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
        "device_id": "test-device"
    }
]'

echo -e "\n${YELLOW}=== 插件系统 HTTP API 测试完成 ===${NC}"
