#!/bin/bash

echo "==================== DevInsight Phase 2 最终测试 ===================="

# 清理函数
cleanup() {
    echo "🧹 清理测试环境..."
    pkill -f control_plane 2>/dev/null
    pkill -f agent 2>/dev/null
    sleep 2
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -i :$port > /dev/null 2>&1; then
        echo "⚠️ 端口 $port 被占用，清理中..."
        lsof -ti :$port | xargs kill -9 2>/dev/null
        sleep 1
    fi
}

# 清理旧进程和端口
cleanup
check_port 50051
check_port 8080

echo "📁 准备测试环境..."
mkdir -p logs /tmp/test_logs

# 创建测试日志
echo "$(date): 测试应用启动" > /tmp/test_logs/app.log
echo "$(date): 系统运行正常" >> /tmp/test_logs/app.log

echo "🚀 启动控制平面..."
cd /Volumes/data/Code/Go/src/aiops/control_plane
nohup ./control_plane > ../logs/control_plane_test.log 2>&1 &
CONTROL_PLANE_PID=$!
echo "控制平面进程 ID: $CONTROL_PLANE_PID"

echo "⏳ 等待控制平面启动..."
sleep 5

# 检查控制平面是否启动成功
if ! kill -0 $CONTROL_PLANE_PID 2>/dev/null; then
    echo "❌ 控制平面启动失败"
    exit 1
fi

# 测试健康检查端点
if curl -s http://localhost:8080/api/health > /dev/null; then
    echo "✅ 控制平面健康检查通过"
else
    echo "⚠️ 控制平面健康检查失败，但继续测试"
fi

echo "🤖 启动 Agent..."
cd /Volumes/data/Code/Go/src/aiops/agent
nohup ./agent -server localhost:50051 > ../logs/agent_test.log 2>&1 &
AGENT_PID=$!
echo "Agent 进程 ID: $AGENT_PID"

echo "⏳ 等待 Agent 连接和注册..."
sleep 10

# 检查进程状态
echo "📊 检查进程状态..."
if kill -0 $CONTROL_PLANE_PID 2>/dev/null; then
    echo "✅ 控制平面运行正常"
else
    echo "❌ 控制平面已停止"
fi

if kill -0 $AGENT_PID 2>/dev/null; then
    echo "✅ Agent 运行正常"
else
    echo "❌ Agent 已停止"
fi

# 测试日志功能
echo "📜 测试日志转发功能..."
for i in {1..5}; do
    echo "$(date): 测试日志消息 $i" >> /tmp/test_logs/app.log
    sleep 1
done

echo "⏳ 等待日志处理..."
sleep 10

# 检查日志转发统计
echo "📈 检查日志转发统计..."
AGENT_LOG_LINES=$(grep "刷新日志缓冲区" ../logs/agent_test.log | tail -1)
if [[ -n "$AGENT_LOG_LINES" ]]; then
    echo "✅ Agent 日志转发正常工作"
    echo "   最新状态: $AGENT_LOG_LINES"
else
    echo "⚠️ 未检测到日志转发活动"
fi

CONTROL_PLANE_LOG_LINES=$(grep "成功保存日志数据" ../logs/control_plane_test.log | tail -1)
if [[ -n "$CONTROL_PLANE_LOG_LINES" ]]; then
    echo "✅ 控制平面日志接收正常"
    echo "   最新状态: $CONTROL_PLANE_LOG_LINES"
else
    echo "⚠️ 控制平面未接收到日志数据"
fi

# 测试连接重连功能
echo "🔄 测试连接重连功能..."
echo "暂停控制平面进程..."
kill -STOP $CONTROL_PLANE_PID
sleep 5

echo "恢复控制平面进程..."
kill -CONT $CONTROL_PLANE_PID
sleep 5

# 检查重连日志
RECONNECT_LOG=$(grep "重连成功" ../logs/agent_test.log | tail -1)
if [[ -n "$RECONNECT_LOG" ]]; then
    echo "✅ Agent 重连功能正常"
else
    echo "⚠️ 未检测到重连活动（可能连接没有断开）"
fi

echo ""
echo "==================== 测试结果摘要 ===================="

# 检查各项功能状态
CONTROL_PLANE_STATUS="✅"
AGENT_STATUS="✅"
LOG_FORWARDING_STATUS="⚠️"
RECONNECTION_STATUS="⚠️"

if ! kill -0 $CONTROL_PLANE_PID 2>/dev/null; then
    CONTROL_PLANE_STATUS="❌"
fi

if ! kill -0 $AGENT_PID 2>/dev/null; then
    AGENT_STATUS="❌"
fi

if [[ -n "$AGENT_LOG_LINES" && -n "$CONTROL_PLANE_LOG_LINES" ]]; then
    LOG_FORWARDING_STATUS="✅"
fi

if [[ -n "$RECONNECT_LOG" ]]; then
    RECONNECTION_STATUS="✅"
fi

echo "$CONTROL_PLANE_STATUS 控制平面状态"
echo "$AGENT_STATUS Agent状态"  
echo "$LOG_FORWARDING_STATUS 日志转发功能"
echo "$RECONNECTION_STATUS 连接重连功能"

echo ""
echo "📋 日志文件位置："
echo "  - 控制平面: logs/control_plane_test.log"
echo "  - Agent: logs/agent_test.log"
echo "  - 测试日志: /tmp/test_logs/app.log"

echo ""
echo "🔍 最新日志摘要："
echo "📋 控制平面日志（最后 3 行）:"
tail -3 ../logs/control_plane_test.log

echo ""
echo "📋 Agent 日志（最后 3 行）:"
tail -3 ../logs/agent_test.log

cleanup

echo ""
echo "==================== DevInsight Phase 2 测试完成 ===================="