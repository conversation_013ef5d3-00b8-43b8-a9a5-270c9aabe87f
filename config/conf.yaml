# 示例配置文件，可以保存为 config.yaml 或 config.json 等格式
logger:
  level: "info"                 # 日志级别: debug, info, warn, error
  format: "json"                # 日志格式: json, text
  file_path: "logs/app.log"     # 日志文件路径
  console_output: true          # 是否同时输出到控制台
  
  # 日志轮转配置
  rotation:
    max_size: 100               # 单个日志文件最大大小，MB
    max_backups: 3              # 保留的旧日志文件的最大数量
    max_age: 7                  # 保留旧日志文件的最大天数
    compress: true              # 是否压缩旧的日志文件
  
  # GORM日志配置
  gorm:
    slow_threshold: 1000        # 慢查询阈值，毫秒
    ignore_record_not_found_error: true  # 是否忽略记录未找到错误
    colorful: false             # 是否启用彩色日志
