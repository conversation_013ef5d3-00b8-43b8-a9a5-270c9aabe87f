env: local
security:
  api_sign:
    app_key: 123456
    app_security: 123456
  jwt:
    key: QQYnRFerJTSEcrfB89fw8prOaObmrch8
    expire: 7200

data:
  db:
#    user:
#      driver: sqlite
#      dsn: storage/nunu-test.db?_busy_timeout=5000
    user:
      driver: mysql
      dsn: zhouqp:4ClnBM65GuIPkOyt@tcp(mysql3.sqlpub.com:3308)/v_admin?charset=utf8mb4&parseTime=True&loc=UTC
#    user:
#      driver: postgres
#      dsn: host=localhost user=gorm password=gorm dbname=gorm port=9920 sslmode=disable TimeZone=Asia/Shanghai

log:
  log_level: debug
  encoding: console           # json or console
  log_file_name: "./logs/agent.log"
  max_backups: 30
  max_age: 7
  max_size: 1024
  compress: true

# Agent 配置
agent:
  # Agent ID 和基本信息
  agent_id: ""               # 如果为空，将自动生成
  agent_ip: ""               # 如果为空，将自动检测
  
  # 支持的采集器类型
  supported_collector_types:
    - "system"
    - "mysql"
    - "redis"
    - "docker"

# 连接管理配置
connection:
  server_addr: "localhost:50051"     # 控制平面服务器地址
  initial_backoff: "1s"              # 初始退避时间
  max_backoff: "5m"                  # 最大退避时间
  backoff_multiplier: 2.0            # 退避倍数
  max_retries: -1                    # 最大重试次数(-1表示无限重试)
  health_check_interval: "30s"       # 健康检查间隔
  connect_timeout: "10s"             # 连接超时

# 采集器健壮性配置
collector:
  # 重试配置
  retry:
    max_retries: 3                   # 最大重试次数
    initial_backoff: "1s"            # 初始退避时间
    max_backoff: "1m"                # 最大退避时间
    backoff_factor: 2.0              # 退避倍数
  
  # 健康检查配置
  health_check:
    interval: "30s"                  # 检查间隔
    timeout: "10s"                   # 超时时间
    failure_threshold: 3             # 失败阈值
  
  # 资源限制配置
  resource_limits:
    max_memory_mb: 512               # 最大内存限制(MB)
    max_goroutines: 100              # 最大协程数
    max_metrics_queue: 1000          # 最大指标队列长度

# 日志转发配置
log_forwarder:
  # 基本配置
  enabled: true                      # 是否启用日志转发
  buffer_size: 100                   # 缓冲区大小
  flush_interval: "5s"               # 刷新间隔
  scan_interval: "1s"                # 文件扫描间隔
  
  # 文件监控配置
  max_files: 50                      # 最大监控文件数
  max_line_length: 4096              # 最大行长度
  
  # 包含和排除模式
  include_patterns:
    - "*.log"
    - "*.txt"
  exclude_patterns:
    - "*.tmp"
    - "*.backup"
  
  # 日志级别过滤
  log_level_filter:
    - "DEBUG"
    - "INFO"
    - "WARN"
    - "ERROR"
  
  # 系统日志监控
  system_logs:
    enabled: true                    # 是否监控系统日志
    paths:
      - "/var/log/syslog"
      - "/var/log/messages"
      - "/var/log/system.log"
  
  # 应用日志监控
  application_logs:
    enabled: true                    # 是否监控应用日志
    directories:
      - "./logs"
      - "/var/log/app"
  

 