#!/bin/bash

echo "=== DevInsight 异常检测简化测试 ==="

BASE_URL="http://localhost:8080/api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local endpoint="$1"
    local description="$2"
    local method="${3:-GET}"
    local data="$4"
    
    echo -e "\n${YELLOW}测试: $description${NC}"
    echo "请求: $method $BASE_URL$endpoint"
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$BASE_URL$endpoint")
    fi
    
    http_code=$(echo "$response" | tail -n1 | cut -d: -f2)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ 成功 (HTTP $http_code)${NC}"
        echo "响应: $body"
    else
        echo -e "${RED}✗ 失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    
    return $http_code
}

echo "=== 1. 健康检查 ==="
test_api "/health" "控制平面健康检查"

echo -e "\n=== 2. 插件系统检查 ==="
test_api "/plugins/health-check" "所有插件健康检查" "POST"

echo -e "\n=== 3. 异常检测测试 ==="

# 创建异常检测请求数据
anomaly_data='{
  "device_id": "test-device-001",
  "device_type": "server",
  "metrics": [
    {
      "key": "cpu_usage",
      "value": 95.5,
      "timestamp": '$(date +%s)',
      "unit": "%"
    },
    {
      "key": "memory_usage", 
      "value": 45.2,
      "timestamp": '$(date +%s)',
      "unit": "%"
    },
    {
      "key": "network_in",
      "value": 1000000,
      "timestamp": '$(date +%s)',
      "unit": "bytes/s"
    }
  ]
}'

test_api "/plugins/detect-anomalies" "异常检测测试" "POST" "$anomaly_data"

echo -e "\n=== 4. 列出所有插件 ==="
test_api "/plugins" "列出所有插件"

echo -e "\n=== 测试完成 ==="
