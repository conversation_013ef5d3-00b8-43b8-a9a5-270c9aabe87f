#!/bin/bash

# 专门测试异常检测功能
set -e

BASE_URL="http://localhost:8080"
TOKEN="dev-token"

echo "=== DevInsight 异常检测专项测试 ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

DEVICE_ID="anomaly-focused-test"
BASE_TIME=1732545000

# 1. 首先累积大量正常数据，建立基线
log_info "1. 累积基线数据（100批次，确保分析器有足够的历史数据）"

for i in {1..100}; do
    START_TIME=$((BASE_TIME + i * 60))  # 每分钟一批
    END_TIME=$((START_TIME + 60))
    
    curl -s -X POST "${BASE_URL}/api/plugins/analyze?token=${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"device_id\": \"${DEVICE_ID}\",
            \"start_time\": ${START_TIME},
            \"end_time\": ${END_TIME},
            \"limit\": 20
        }" > /dev/null
    
    # 每20次输出一次进度
    if [[ $((i % 20)) -eq 0 ]]; then
        log_info "已累积 $i 批基线数据"
    fi
done

log_success "基线数据累积完成，共100批数据"

# 2. 等待一下，让数据稳定
sleep 1

# 3. 测试不同阈值的异常检测
log_info "2. 测试不同阈值的异常检测效果"

thresholds=(0.5 1.0 1.5 2.0 2.5 3.0)

for threshold in "${thresholds[@]}"; do
    log_info "测试阈值: $threshold"
    
    # 使用较大的时间范围来包含更多数据
    DETECT_START=$((BASE_TIME + 500))
    DETECT_END=$((BASE_TIME + 7000))
    
    response=$(curl -s -X POST "${BASE_URL}/api/plugins/detect-anomalies?token=${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"device_id\": \"${DEVICE_ID}\",
            \"start_time\": ${DETECT_START},
            \"end_time\": ${DETECT_END},
            \"threshold\": ${threshold}
        }")
    
    echo "响应:"
    echo "$response" | jq
    
    # 检查是否有异常
    anomaly_count=$(echo "$response" | jq '.data.anomaly_count // 0')
    if [[ "$anomaly_count" -gt 0 ]]; then
        log_success "阈值 $threshold: 发现 $anomaly_count 个异常"
    else
        log_warning "阈值 $threshold: 未发现异常"
    fi
    
    echo
    echo "---"
    echo
done

# 4. 检查系统运行状态
log_info "3. 检查系统运行状态"

# 检查插件健康状态
log_info "检查插件健康状态..."
health_response=$(curl -s -X POST "${BASE_URL}/api/plugins/health-check?token=${TOKEN}")
echo "插件健康状态:"
echo "$health_response" | jq

echo
log_info "检查enhanced-analyzer插件状态..."
enhanced_health=$(curl -s "${BASE_URL}/api/plugins/enhanced-analyzer/1.0.0/health?token=${TOKEN}")
echo "Enhanced Analyzer健康状态:"
echo "$enhanced_health" | jq

echo
log_info "检查simple-analyzer插件状态..."
simple_health=$(curl -s "${BASE_URL}/api/plugins/simple-analyzer/1.0.0/health?token=${TOKEN}")
echo "Simple Analyzer健康状态:"
echo "$simple_health" | jq

# 5. 最后的总结分析
echo
echo "===================="
log_success "异常检测专项测试完成"
echo "===================="
echo
log_info "测试总结:"
echo "- 累积了100批基线数据用于建立统计模型"
echo "- 测试了6种不同的异常检测阈值"
echo "- 验证了分析器插件的健康状态"
echo
log_info "如果仍未检测到异常，可能的原因："
echo "1. 生成的测试数据虽然有峰值，但可能仍在统计正常范围内"
echo "2. 分析器缓冲区大小限制了历史数据量"
echo "3. Z-score计算方法对于当前数据模式不够敏感"
echo "4. 需要更极端的异常数据来触发检测"
echo
log_info "建议下一步："
echo "- 检查插件的实际缓冲区数据量"
echo "- 调整异常检测算法的敏感度"
echo "- 或者生成更极端的测试数据"
