// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: pkg/proto/devinsight.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RegisterAgentRequest Agent 注册请求
type RegisterAgentRequest struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	AgentId                 string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentIp                 string                 `protobuf:"bytes,2,opt,name=agent_ip,json=agentIp,proto3" json:"agent_ip,omitempty"`
	SupportedCollectorTypes []string               `protobuf:"bytes,3,rep,name=supported_collector_types,json=supportedCollectorTypes,proto3" json:"supported_collector_types,omitempty"`
	DeviceConfig            *DeviceConfig          `protobuf:"bytes,4,opt,name=device_config,json=deviceConfig,proto3" json:"device_config,omitempty"` // Agent设备配置信息
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *RegisterAgentRequest) Reset() {
	*x = RegisterAgentRequest{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterAgentRequest) ProtoMessage() {}

func (x *RegisterAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterAgentRequest.ProtoReflect.Descriptor instead.
func (*RegisterAgentRequest) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterAgentRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *RegisterAgentRequest) GetAgentIp() string {
	if x != nil {
		return x.AgentIp
	}
	return ""
}

func (x *RegisterAgentRequest) GetSupportedCollectorTypes() []string {
	if x != nil {
		return x.SupportedCollectorTypes
	}
	return nil
}

func (x *RegisterAgentRequest) GetDeviceConfig() *DeviceConfig {
	if x != nil {
		return x.DeviceConfig
	}
	return nil
}

// DeviceConfig Agent设备配置信息
type DeviceConfig struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	MaxMemoryMb        int64                  `protobuf:"varint,1,opt,name=max_memory_mb,json=maxMemoryMb,proto3" json:"max_memory_mb,omitempty"`
	MaxCpuPercent      int32                  `protobuf:"varint,2,opt,name=max_cpu_percent,json=maxCpuPercent,proto3" json:"max_cpu_percent,omitempty"`
	MaxDiskMb          int64                  `protobuf:"varint,3,opt,name=max_disk_mb,json=maxDiskMb,proto3" json:"max_disk_mb,omitempty"`
	MaxConcurrentTasks int32                  `protobuf:"varint,4,opt,name=max_concurrent_tasks,json=maxConcurrentTasks,proto3" json:"max_concurrent_tasks,omitempty"`
	Capabilities       map[string]string      `protobuf:"bytes,5,rep,name=capabilities,proto3" json:"capabilities,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *DeviceConfig) Reset() {
	*x = DeviceConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceConfig) ProtoMessage() {}

func (x *DeviceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceConfig.ProtoReflect.Descriptor instead.
func (*DeviceConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceConfig) GetMaxMemoryMb() int64 {
	if x != nil {
		return x.MaxMemoryMb
	}
	return 0
}

func (x *DeviceConfig) GetMaxCpuPercent() int32 {
	if x != nil {
		return x.MaxCpuPercent
	}
	return 0
}

func (x *DeviceConfig) GetMaxDiskMb() int64 {
	if x != nil {
		return x.MaxDiskMb
	}
	return 0
}

func (x *DeviceConfig) GetMaxConcurrentTasks() int32 {
	if x != nil {
		return x.MaxConcurrentTasks
	}
	return 0
}

func (x *DeviceConfig) GetCapabilities() map[string]string {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

// RegisterAgentResponse Agent 注册响应
type RegisterAgentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterAgentResponse) Reset() {
	*x = RegisterAgentResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterAgentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterAgentResponse) ProtoMessage() {}

func (x *RegisterAgentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterAgentResponse.ProtoReflect.Descriptor instead.
func (*RegisterAgentResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{2}
}

func (x *RegisterAgentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RegisterAgentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// CollectorTaskConfig 采集任务配置
type CollectorTaskConfig struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TaskId           string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	DeviceId         string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	DeviceName       string                 `protobuf:"bytes,3,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
	DeviceType       string                 `protobuf:"bytes,4,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	Host             string                 `protobuf:"bytes,5,opt,name=host,proto3" json:"host,omitempty"`
	Port             int32                  `protobuf:"varint,6,opt,name=port,proto3" json:"port,omitempty"`
	Username         string                 `protobuf:"bytes,7,opt,name=username,proto3" json:"username,omitempty"`
	Password         string                 `protobuf:"bytes,8,opt,name=password,proto3" json:"password,omitempty"`
	ConnectParams    map[string]string      `protobuf:"bytes,9,rep,name=connect_params,json=connectParams,proto3" json:"connect_params,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	FrequencySeconds int64                  `protobuf:"varint,10,opt,name=frequency_seconds,json=frequencySeconds,proto3" json:"frequency_seconds,omitempty"`
	CollectItems     []string               `protobuf:"bytes,11,rep,name=collect_items,json=collectItems,proto3" json:"collect_items,omitempty"`
	IsEnabled        bool                   `protobuf:"varint,12,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CollectorTaskConfig) Reset() {
	*x = CollectorTaskConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CollectorTaskConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectorTaskConfig) ProtoMessage() {}

func (x *CollectorTaskConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectorTaskConfig.ProtoReflect.Descriptor instead.
func (*CollectorTaskConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{3}
}

func (x *CollectorTaskConfig) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *CollectorTaskConfig) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CollectorTaskConfig) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *CollectorTaskConfig) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *CollectorTaskConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *CollectorTaskConfig) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *CollectorTaskConfig) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CollectorTaskConfig) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CollectorTaskConfig) GetConnectParams() map[string]string {
	if x != nil {
		return x.ConnectParams
	}
	return nil
}

func (x *CollectorTaskConfig) GetFrequencySeconds() int64 {
	if x != nil {
		return x.FrequencySeconds
	}
	return 0
}

func (x *CollectorTaskConfig) GetCollectItems() []string {
	if x != nil {
		return x.CollectItems
	}
	return nil
}

func (x *CollectorTaskConfig) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

// TaskStatus 任务状态上报
type TaskStatus struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TaskId               string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Status               string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"` // running, failed, success
	ErrorMessage         string                 `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	LastCollectTimestamp int64                  `protobuf:"varint,4,opt,name=last_collect_timestamp,json=lastCollectTimestamp,proto3" json:"last_collect_timestamp,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *TaskStatus) Reset() {
	*x = TaskStatus{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStatus) ProtoMessage() {}

func (x *TaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStatus.ProtoReflect.Descriptor instead.
func (*TaskStatus) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{4}
}

func (x *TaskStatus) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TaskStatus) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *TaskStatus) GetLastCollectTimestamp() int64 {
	if x != nil {
		return x.LastCollectTimestamp
	}
	return 0
}

// MetricData 指标数据点 - 支持灵活的多维数据
type MetricData struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	DeviceId  string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	MetricKey string                 `protobuf:"bytes,2,opt,name=metric_key,json=metricKey,proto3" json:"metric_key,omitempty"`
	// Types that are valid to be assigned to ValueType:
	//
	//	*MetricData_NumericValue
	//	*MetricData_StringValue
	//	*MetricData_BooleanValue
	ValueType     isMetricData_ValueType `protobuf_oneof:"value_type"`
	Timestamp     int64                  `protobuf:"varint,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	JsonData      string                 `protobuf:"bytes,7,opt,name=json_data,json=jsonData,proto3" json:"json_data,omitempty"` // 灵活的JSON格式数据，支持复杂的多维指标
	Labels        map[string]string      `protobuf:"bytes,8,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetricData) Reset() {
	*x = MetricData{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetricData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetricData) ProtoMessage() {}

func (x *MetricData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetricData.ProtoReflect.Descriptor instead.
func (*MetricData) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{5}
}

func (x *MetricData) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *MetricData) GetMetricKey() string {
	if x != nil {
		return x.MetricKey
	}
	return ""
}

func (x *MetricData) GetValueType() isMetricData_ValueType {
	if x != nil {
		return x.ValueType
	}
	return nil
}

func (x *MetricData) GetNumericValue() float64 {
	if x != nil {
		if x, ok := x.ValueType.(*MetricData_NumericValue); ok {
			return x.NumericValue
		}
	}
	return 0
}

func (x *MetricData) GetStringValue() string {
	if x != nil {
		if x, ok := x.ValueType.(*MetricData_StringValue); ok {
			return x.StringValue
		}
	}
	return ""
}

func (x *MetricData) GetBooleanValue() bool {
	if x != nil {
		if x, ok := x.ValueType.(*MetricData_BooleanValue); ok {
			return x.BooleanValue
		}
	}
	return false
}

func (x *MetricData) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *MetricData) GetJsonData() string {
	if x != nil {
		return x.JsonData
	}
	return ""
}

func (x *MetricData) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type isMetricData_ValueType interface {
	isMetricData_ValueType()
}

type MetricData_NumericValue struct {
	NumericValue float64 `protobuf:"fixed64,3,opt,name=numeric_value,json=numericValue,proto3,oneof"`
}

type MetricData_StringValue struct {
	StringValue string `protobuf:"bytes,4,opt,name=string_value,json=stringValue,proto3,oneof"`
}

type MetricData_BooleanValue struct {
	BooleanValue bool `protobuf:"varint,5,opt,name=boolean_value,json=booleanValue,proto3,oneof"`
}

func (*MetricData_NumericValue) isMetricData_ValueType() {}

func (*MetricData_StringValue) isMetricData_ValueType() {}

func (*MetricData_BooleanValue) isMetricData_ValueType() {}

// SupportedMetric 支持的指标定义
type SupportedMetric struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MetricKey     string                 `protobuf:"bytes,1,opt,name=metric_key,json=metricKey,proto3" json:"metric_key,omitempty"`
	MetricName    string                 `protobuf:"bytes,2,opt,name=metric_name,json=metricName,proto3" json:"metric_name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	DataType      string                 `protobuf:"bytes,4,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"` // numeric, string, boolean, json
	Unit          string                 `protobuf:"bytes,5,opt,name=unit,proto3" json:"unit,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,6,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	IsActive      bool                   `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CollectorType string                 `protobuf:"bytes,8,opt,name=collector_type,json=collectorType,proto3" json:"collector_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SupportedMetric) Reset() {
	*x = SupportedMetric{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupportedMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportedMetric) ProtoMessage() {}

func (x *SupportedMetric) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportedMetric.ProtoReflect.Descriptor instead.
func (*SupportedMetric) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{6}
}

func (x *SupportedMetric) GetMetricKey() string {
	if x != nil {
		return x.MetricKey
	}
	return ""
}

func (x *SupportedMetric) GetMetricName() string {
	if x != nil {
		return x.MetricName
	}
	return ""
}

func (x *SupportedMetric) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SupportedMetric) GetDataType() string {
	if x != nil {
		return x.DataType
	}
	return ""
}

func (x *SupportedMetric) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *SupportedMetric) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SupportedMetric) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *SupportedMetric) GetCollectorType() string {
	if x != nil {
		return x.CollectorType
	}
	return ""
}

// LogEntry 日志条目
type LogEntry struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	LogLevel      string                 `protobuf:"bytes,2,opt,name=log_level,json=logLevel,proto3" json:"log_level,omitempty"` // DEBUG, INFO, WARN, ERROR
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp     int64                  `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Source        string                 `protobuf:"bytes,5,opt,name=source,proto3" json:"source,omitempty"`                                                                           // 日志来源：agent, collector, etc.
	Fields        map[string]string      `protobuf:"bytes,6,rep,name=fields,proto3" json:"fields,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 额外的结构化字段
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogEntry) Reset() {
	*x = LogEntry{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEntry) ProtoMessage() {}

func (x *LogEntry) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEntry.ProtoReflect.Descriptor instead.
func (*LogEntry) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{7}
}

func (x *LogEntry) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *LogEntry) GetLogLevel() string {
	if x != nil {
		return x.LogLevel
	}
	return ""
}

func (x *LogEntry) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LogEntry) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *LogEntry) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *LogEntry) GetFields() map[string]string {
	if x != nil {
		return x.Fields
	}
	return nil
}

// StreamLogDataResponse 日志数据上报响应
type StreamLogDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ReceivedCount int32                  `protobuf:"varint,3,opt,name=received_count,json=receivedCount,proto3" json:"received_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamLogDataResponse) Reset() {
	*x = StreamLogDataResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamLogDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamLogDataResponse) ProtoMessage() {}

func (x *StreamLogDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamLogDataResponse.ProtoReflect.Descriptor instead.
func (*StreamLogDataResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{8}
}

func (x *StreamLogDataResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *StreamLogDataResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StreamLogDataResponse) GetReceivedCount() int32 {
	if x != nil {
		return x.ReceivedCount
	}
	return 0
}

// StreamMetricDataResponse 指标数据上报响应
type StreamMetricDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ReceivedCount int32                  `protobuf:"varint,3,opt,name=received_count,json=receivedCount,proto3" json:"received_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamMetricDataResponse) Reset() {
	*x = StreamMetricDataResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamMetricDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamMetricDataResponse) ProtoMessage() {}

func (x *StreamMetricDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamMetricDataResponse.ProtoReflect.Descriptor instead.
func (*StreamMetricDataResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{9}
}

func (x *StreamMetricDataResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *StreamMetricDataResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StreamMetricDataResponse) GetReceivedCount() int32 {
	if x != nil {
		return x.ReceivedCount
	}
	return 0
}

var File_pkg_proto_devinsight_proto protoreflect.FileDescriptor

const file_pkg_proto_devinsight_proto_rawDesc = "" +
	"\n" +
	"\x1apkg/proto/devinsight.proto\x12\n" +
	"devinsight\"\xc7\x01\n" +
	"\x14RegisterAgentRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x19\n" +
	"\bagent_ip\x18\x02 \x01(\tR\aagentIp\x12:\n" +
	"\x19supported_collector_types\x18\x03 \x03(\tR\x17supportedCollectorTypes\x12=\n" +
	"\rdevice_config\x18\x04 \x01(\v2\x18.devinsight.DeviceConfigR\fdeviceConfig\"\xbd\x02\n" +
	"\fDeviceConfig\x12\"\n" +
	"\rmax_memory_mb\x18\x01 \x01(\x03R\vmaxMemoryMb\x12&\n" +
	"\x0fmax_cpu_percent\x18\x02 \x01(\x05R\rmaxCpuPercent\x12\x1e\n" +
	"\vmax_disk_mb\x18\x03 \x01(\x03R\tmaxDiskMb\x120\n" +
	"\x14max_concurrent_tasks\x18\x04 \x01(\x05R\x12maxConcurrentTasks\x12N\n" +
	"\fcapabilities\x18\x05 \x03(\v2*.devinsight.DeviceConfig.CapabilitiesEntryR\fcapabilities\x1a?\n" +
	"\x11CapabilitiesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"K\n" +
	"\x15RegisterAgentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xfb\x03\n" +
	"\x13CollectorTaskConfig\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\x12\x1f\n" +
	"\vdevice_name\x18\x03 \x01(\tR\n" +
	"deviceName\x12\x1f\n" +
	"\vdevice_type\x18\x04 \x01(\tR\n" +
	"deviceType\x12\x12\n" +
	"\x04host\x18\x05 \x01(\tR\x04host\x12\x12\n" +
	"\x04port\x18\x06 \x01(\x05R\x04port\x12\x1a\n" +
	"\busername\x18\a \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\b \x01(\tR\bpassword\x12Y\n" +
	"\x0econnect_params\x18\t \x03(\v22.devinsight.CollectorTaskConfig.ConnectParamsEntryR\rconnectParams\x12+\n" +
	"\x11frequency_seconds\x18\n" +
	" \x01(\x03R\x10frequencySeconds\x12#\n" +
	"\rcollect_items\x18\v \x03(\tR\fcollectItems\x12\x1d\n" +
	"\n" +
	"is_enabled\x18\f \x01(\bR\tisEnabled\x1a@\n" +
	"\x12ConnectParamsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x98\x01\n" +
	"\n" +
	"TaskStatus\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\x124\n" +
	"\x16last_collect_timestamp\x18\x04 \x01(\x03R\x14lastCollectTimestamp\"\xfb\x02\n" +
	"\n" +
	"MetricData\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1d\n" +
	"\n" +
	"metric_key\x18\x02 \x01(\tR\tmetricKey\x12%\n" +
	"\rnumeric_value\x18\x03 \x01(\x01H\x00R\fnumericValue\x12#\n" +
	"\fstring_value\x18\x04 \x01(\tH\x00R\vstringValue\x12%\n" +
	"\rboolean_value\x18\x05 \x01(\bH\x00R\fbooleanValue\x12\x1c\n" +
	"\ttimestamp\x18\x06 \x01(\x03R\ttimestamp\x12\x1b\n" +
	"\tjson_data\x18\a \x01(\tR\bjsonData\x12:\n" +
	"\x06labels\x18\b \x03(\v2\".devinsight.MetricData.LabelsEntryR\x06labels\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\f\n" +
	"\n" +
	"value_type\"\xec\x02\n" +
	"\x0fSupportedMetric\x12\x1d\n" +
	"\n" +
	"metric_key\x18\x01 \x01(\tR\tmetricKey\x12\x1f\n" +
	"\vmetric_name\x18\x02 \x01(\tR\n" +
	"metricName\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1b\n" +
	"\tdata_type\x18\x04 \x01(\tR\bdataType\x12\x12\n" +
	"\x04unit\x18\x05 \x01(\tR\x04unit\x12E\n" +
	"\bmetadata\x18\x06 \x03(\v2).devinsight.SupportedMetric.MetadataEntryR\bmetadata\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\x12%\n" +
	"\x0ecollector_type\x18\b \x01(\tR\rcollectorType\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x89\x02\n" +
	"\bLogEntry\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1b\n" +
	"\tlog_level\x18\x02 \x01(\tR\blogLevel\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12\x1c\n" +
	"\ttimestamp\x18\x04 \x01(\x03R\ttimestamp\x12\x16\n" +
	"\x06source\x18\x05 \x01(\tR\x06source\x128\n" +
	"\x06fields\x18\x06 \x03(\v2 .devinsight.LogEntry.FieldsEntryR\x06fields\x1a9\n" +
	"\vFieldsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"r\n" +
	"\x15StreamLogDataResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x0ereceived_count\x18\x03 \x01(\x05R\rreceivedCount\"u\n" +
	"\x18StreamMetricDataResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x0ereceived_count\x18\x03 \x01(\x05R\rreceivedCount2\xe1\x02\n" +
	"\fAgentService\x12V\n" +
	"\rRegisterAgent\x12 .devinsight.RegisterAgentRequest\x1a!.devinsight.RegisterAgentResponse\"\x00\x12U\n" +
	"\x14StreamCollectorTasks\x12\x16.devinsight.TaskStatus\x1a\x1f.devinsight.CollectorTaskConfig\"\x00(\x010\x01\x12T\n" +
	"\x10StreamMetricData\x12\x16.devinsight.MetricData\x1a$.devinsight.StreamMetricDataResponse\"\x00(\x01\x12L\n" +
	"\rStreamLogData\x12\x14.devinsight.LogEntry\x1a!.devinsight.StreamLogDataResponse\"\x00(\x01B\x11Z\x0faiops/pkg/protob\x06proto3"

var (
	file_pkg_proto_devinsight_proto_rawDescOnce sync.Once
	file_pkg_proto_devinsight_proto_rawDescData []byte
)

func file_pkg_proto_devinsight_proto_rawDescGZIP() []byte {
	file_pkg_proto_devinsight_proto_rawDescOnce.Do(func() {
		file_pkg_proto_devinsight_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pkg_proto_devinsight_proto_rawDesc), len(file_pkg_proto_devinsight_proto_rawDesc)))
	})
	return file_pkg_proto_devinsight_proto_rawDescData
}

var file_pkg_proto_devinsight_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_pkg_proto_devinsight_proto_goTypes = []any{
	(*RegisterAgentRequest)(nil),     // 0: devinsight.RegisterAgentRequest
	(*DeviceConfig)(nil),             // 1: devinsight.DeviceConfig
	(*RegisterAgentResponse)(nil),    // 2: devinsight.RegisterAgentResponse
	(*CollectorTaskConfig)(nil),      // 3: devinsight.CollectorTaskConfig
	(*TaskStatus)(nil),               // 4: devinsight.TaskStatus
	(*MetricData)(nil),               // 5: devinsight.MetricData
	(*SupportedMetric)(nil),          // 6: devinsight.SupportedMetric
	(*LogEntry)(nil),                 // 7: devinsight.LogEntry
	(*StreamLogDataResponse)(nil),    // 8: devinsight.StreamLogDataResponse
	(*StreamMetricDataResponse)(nil), // 9: devinsight.StreamMetricDataResponse
	nil,                              // 10: devinsight.DeviceConfig.CapabilitiesEntry
	nil,                              // 11: devinsight.CollectorTaskConfig.ConnectParamsEntry
	nil,                              // 12: devinsight.MetricData.LabelsEntry
	nil,                              // 13: devinsight.SupportedMetric.MetadataEntry
	nil,                              // 14: devinsight.LogEntry.FieldsEntry
}
var file_pkg_proto_devinsight_proto_depIdxs = []int32{
	1,  // 0: devinsight.RegisterAgentRequest.device_config:type_name -> devinsight.DeviceConfig
	10, // 1: devinsight.DeviceConfig.capabilities:type_name -> devinsight.DeviceConfig.CapabilitiesEntry
	11, // 2: devinsight.CollectorTaskConfig.connect_params:type_name -> devinsight.CollectorTaskConfig.ConnectParamsEntry
	12, // 3: devinsight.MetricData.labels:type_name -> devinsight.MetricData.LabelsEntry
	13, // 4: devinsight.SupportedMetric.metadata:type_name -> devinsight.SupportedMetric.MetadataEntry
	14, // 5: devinsight.LogEntry.fields:type_name -> devinsight.LogEntry.FieldsEntry
	0,  // 6: devinsight.AgentService.RegisterAgent:input_type -> devinsight.RegisterAgentRequest
	4,  // 7: devinsight.AgentService.StreamCollectorTasks:input_type -> devinsight.TaskStatus
	5,  // 8: devinsight.AgentService.StreamMetricData:input_type -> devinsight.MetricData
	7,  // 9: devinsight.AgentService.StreamLogData:input_type -> devinsight.LogEntry
	2,  // 10: devinsight.AgentService.RegisterAgent:output_type -> devinsight.RegisterAgentResponse
	3,  // 11: devinsight.AgentService.StreamCollectorTasks:output_type -> devinsight.CollectorTaskConfig
	9,  // 12: devinsight.AgentService.StreamMetricData:output_type -> devinsight.StreamMetricDataResponse
	8,  // 13: devinsight.AgentService.StreamLogData:output_type -> devinsight.StreamLogDataResponse
	10, // [10:14] is the sub-list for method output_type
	6,  // [6:10] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_pkg_proto_devinsight_proto_init() }
func file_pkg_proto_devinsight_proto_init() {
	if File_pkg_proto_devinsight_proto != nil {
		return
	}
	file_pkg_proto_devinsight_proto_msgTypes[5].OneofWrappers = []any{
		(*MetricData_NumericValue)(nil),
		(*MetricData_StringValue)(nil),
		(*MetricData_BooleanValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pkg_proto_devinsight_proto_rawDesc), len(file_pkg_proto_devinsight_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pkg_proto_devinsight_proto_goTypes,
		DependencyIndexes: file_pkg_proto_devinsight_proto_depIdxs,
		MessageInfos:      file_pkg_proto_devinsight_proto_msgTypes,
	}.Build()
	File_pkg_proto_devinsight_proto = out.File
	file_pkg_proto_devinsight_proto_goTypes = nil
	file_pkg_proto_devinsight_proto_depIdxs = nil
}
