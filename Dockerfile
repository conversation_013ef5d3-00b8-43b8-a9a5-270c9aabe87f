FROM golang:1.24-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache git ca-certificates tzdata

# 复制 go.mod 和 go.sum
COPY go.mod ./
RUN go mod download

# 复制源代码
COPY . .

# 构建控制平面
RUN CGO_ENABLED=0 GOOS=linux go build -o /app/bin/devinsight-control-plane ./control_plane/cmd

# 构建代理
RUN CGO_ENABLED=0 GOOS=linux go build -o /app/bin/devinsight-agent ./agent/cmd

# 使用多阶段构建，为控制平面创建更小的容器
FROM alpine:latest AS control-plane

# 设置时区为亚洲/上海
RUN apk add --no-cache tzdata
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN adduser -D -u 1001 -g 'devinsight' devinsight

# 创建工作目录
WORKDIR /app

# 复制可执行文件
COPY --from=builder /app/bin/devinsight-control-plane /app/devinsight-control-plane

# 设置权限
RUN chmod +x /app/devinsight-control-plane && \
    chown -R devinsight:devinsight /app

# 切换到非root用户
USER devinsight

# 暴露端口
EXPOSE 8080 50051

# 设置入口点
ENTRYPOINT ["/app/devinsight-control-plane"]

# 为代理创建更小的容器
FROM alpine:latest AS agent

# 设置时区为亚洲/上海
RUN apk add --no-cache tzdata
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN adduser -D -u 1001 -g 'devinsight' devinsight

# 创建工作目录
WORKDIR /app

# 复制可执行文件
COPY --from=builder /app/bin/devinsight-agent /app/devinsight-agent

# 设置权限
RUN chmod +x /app/devinsight-agent && \
    chown -R devinsight:devinsight /app

# 切换到非root用户
USER devinsight

# 设置入口点
ENTRYPOINT ["/app/devinsight-agent"]
