package internal

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"aiops/pkg/log"
	"aiops/pkg/proto"

	"go.uber.org/zap"
)

// LogForwarder 日志转发器
type LogForwarder struct {
	logger     *log.Logger
	deviceID   string // 设备ID
	logChannel chan *proto.LogEntry
	ctx        context.Context
	cancel     context.CancelFunc
	mu         sync.RWMutex

	// 配置
	config LogForwarderConfig

	// 文件监控
	watchers map[string]*FileWatcher

	// 缓冲和批处理
	buffer      []*proto.LogEntry
	bufferSize  int
	bufferMutex sync.Mutex

	// 统计信息
	stats LogForwarderStats
}

// LogForwarderConfig 日志转发配置
type LogForwarderConfig struct {
	// 缓冲配置
	BufferSize    int           // 缓冲区大小
	FlushInterval time.Duration // 刷新间隔
	MaxLineLength int           // 最大行长度

	// 监控配置
	ScanInterval time.Duration // 扫描间隔
	MaxFiles     int           // 最大文件数

	// 过滤配置
	IncludePatterns []string // 包含模式
	ExcludePatterns []string // 排除模式
	LogLevelFilter  []string // 日志级别过滤
}

// FileWatcher 文件监控器
type FileWatcher struct {
	FilePath     string
	File         *os.File
	Reader       *bufio.Reader
	LastPosition int64
	LastModTime  time.Time
	LogSource    string
	IsActive     bool
	ParseConfig  LogParseConfig
}

// LogParseConfig 日志解析配置
type LogParseConfig struct {
	TimeFormat   string                    // 时间格式
	LevelRegex   *regexp.Regexp            // 级别正则
	MessageRegex *regexp.Regexp            // 消息正则
	FieldsRegex  map[string]*regexp.Regexp // 字段正则
}

// LogForwarderStats 日志转发统计
type LogForwarderStats struct {
	TotalLogs      int64     // 总日志数
	ForwardedLogs  int64     // 转发日志数
	ErrorLogs      int64     // 错误日志数
	DroppedLogs    int64     // 丢弃日志数
	ActiveWatchers int       // 活跃监控器数
	LastForward    time.Time // 最后转发时间
	BufferUsage    int       // 缓冲区使用率
}

// NewLogForwarder 创建日志转发器
func NewLogForwarder(logger *log.Logger, deviceID string) *LogForwarder {
	ctx, cancel := context.WithCancel(context.Background())

	config := LogForwarderConfig{
		BufferSize:      100,
		FlushInterval:   5 * time.Second,
		MaxLineLength:   10240, // 10KB
		ScanInterval:    10 * time.Second,
		MaxFiles:        50,
		IncludePatterns: []string{"*.log", "*.txt"},
		ExcludePatterns: []string{"*.tmp", "*.backup"},
		LogLevelFilter:  []string{"DEBUG", "INFO", "WARN", "ERROR"},
	}

	forwarder := &LogForwarder{
		logger:     logger,
		deviceID:   deviceID,                          // 设置设备ID
		logChannel: make(chan *proto.LogEntry, 10000), // 增大缓冲队列从1000到10000
		ctx:        ctx,
		cancel:     cancel,
		config:     config,
		watchers:   make(map[string]*FileWatcher),
		buffer:     make([]*proto.LogEntry, 0, config.BufferSize),
		bufferSize: config.BufferSize,
	}

	// 注册日志通道（类似指标通道）
	RegisterLogChannel(forwarder.logChannel)

	// 启动后台服务
	go forwarder.bufferProcessor()
	go forwarder.fileScanner()

	return forwarder
}

// RegisterLogChannel 注册日志通道
func RegisterLogChannel(ch chan *proto.LogEntry) {
	logChannelMutex.Lock()
	defer logChannelMutex.Unlock()
	logChannels = append(logChannels, ch)
}

// 全局日志通道管理
var (
	logChannels     = make([]chan *proto.LogEntry, 0)
	logChannelMutex sync.RWMutex
)

// AddLogWatch 添加日志文件监控
func (lf *LogForwarder) AddLogWatch(filePath, source string, parseConfig LogParseConfig) error {
	lf.mu.Lock()
	defer lf.mu.Unlock()

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("日志文件不存在: %s", filePath)
	}

	// 检查是否已经在监控
	if _, exists := lf.watchers[filePath]; exists {
		return fmt.Errorf("文件已在监控中: %s", filePath)
	}

	// 检查文件数限制
	if len(lf.watchers) >= lf.config.MaxFiles {
		return fmt.Errorf("已达到最大监控文件数限制: %d", lf.config.MaxFiles)
	}

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %w", err)
	}

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		file.Close()
		return fmt.Errorf("获取文件信息失败: %w", err)
	}

	// 移动到文件末尾（只读取新内容）
	_, err = file.Seek(0, io.SeekEnd)
	if err != nil {
		file.Close()
		return fmt.Errorf("定位文件末尾失败: %w", err)
	}

	watcher := &FileWatcher{
		FilePath:     filePath,
		File:         file,
		Reader:       bufio.NewReader(file),
		LastPosition: fileInfo.Size(),
		LastModTime:  fileInfo.ModTime(),
		LogSource:    source,
		IsActive:     true,
		ParseConfig:  parseConfig,
	}

	lf.watchers[filePath] = watcher
	lf.stats.ActiveWatchers = len(lf.watchers)

	lf.logger.Info("添加日志文件监控",
		zap.String("file", filePath),
		zap.String("source", source))

	return nil
}

// RemoveLogWatch 移除日志文件监控
func (lf *LogForwarder) RemoveLogWatch(filePath string) {
	lf.mu.Lock()
	defer lf.mu.Unlock()

	if watcher, exists := lf.watchers[filePath]; exists {
		watcher.IsActive = false
		watcher.File.Close()
		delete(lf.watchers, filePath)
		lf.stats.ActiveWatchers = len(lf.watchers)

		lf.logger.Info("移除日志文件监控", zap.String("file", filePath))
	}
}

// fileScanner 文件扫描器
func (lf *LogForwarder) fileScanner() {
	ticker := time.NewTicker(lf.config.ScanInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			lf.scanLogFiles()
		case <-lf.ctx.Done():
			return
		}
	}
}

// scanLogFiles 扫描日志文件变化
func (lf *LogForwarder) scanLogFiles() {
	lf.mu.RLock()
	watchers := make(map[string]*FileWatcher)
	for k, v := range lf.watchers {
		if v.IsActive {
			watchers[k] = v
		}
	}
	lf.mu.RUnlock()

	for filePath, watcher := range watchers {
		go lf.scanSingleFile(filePath, watcher)
	}
}

// scanSingleFile 扫描单个文件
func (lf *LogForwarder) scanSingleFile(filePath string, watcher *FileWatcher) {
	defer func() {
		if r := recover(); r != nil {
			lf.logger.Error("扫描文件时发生panic",
				zap.String("file", filePath),
				zap.Any("panic", r))
		}
	}()

	// 检查文件是否还存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			lf.logger.Warn("监控的日志文件已被删除", zap.String("file", filePath))
			lf.RemoveLogWatch(filePath)
		}
		return
	}

	// 检查文件是否被截断或重新创建
	if fileInfo.Size() < watcher.LastPosition {
		lf.logger.Info("检测到文件被截断，重新开始读取", zap.String("file", filePath))
		watcher.File.Close()

		// 重新打开文件
		file, err := os.Open(filePath)
		if err != nil {
			lf.logger.Error("重新打开文件失败", zap.String("file", filePath), zap.Error(err))
			return
		}

		watcher.File = file
		watcher.Reader = bufio.NewReader(file)
		watcher.LastPosition = 0
	}

	// 检查文件是否有新内容
	if fileInfo.ModTime().After(watcher.LastModTime) || fileInfo.Size() > watcher.LastPosition {
		lf.readNewLines(watcher)
		watcher.LastModTime = fileInfo.ModTime()
	}
}

// readNewLines 读取新行
func (lf *LogForwarder) readNewLines(watcher *FileWatcher) {
	// 移动到上次读取位置
	_, err := watcher.File.Seek(watcher.LastPosition, io.SeekStart)
	if err != nil {
		lf.logger.Error("定位文件位置失败",
			zap.String("file", watcher.FilePath),
			zap.Error(err))
		return
	}

	watcher.Reader = bufio.NewReader(watcher.File)

	for {
		line, err := watcher.Reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				// 文件结束，更新位置
				position, _ := watcher.File.Seek(0, io.SeekCurrent)
				watcher.LastPosition = position
				break
			}
			lf.logger.Error("读取文件行失败",
				zap.String("file", watcher.FilePath),
				zap.Error(err))
			break
		}

		// 移除换行符
		line = strings.TrimRight(line, "\r\n")

		// 检查行长度
		if len(line) > lf.config.MaxLineLength {
			lf.logger.Warn("日志行过长，截断处理",
				zap.String("file", watcher.FilePath),
				zap.Int("length", len(line)))
			line = line[:lf.config.MaxLineLength] + "...[截断]"
		}

		// 解析并转发日志
		logEntry := lf.parseLogLine(line, watcher)
		if logEntry != nil {
			lf.forwardLog(logEntry)
		}
	}
}

// parseLogLine 解析日志行
func (lf *LogForwarder) parseLogLine(line string, watcher *FileWatcher) *proto.LogEntry {
	if line == "" {
		return nil
	}

	logEntry := &proto.LogEntry{
		DeviceId:  lf.deviceID, // 设置正确的设备ID
		Message:   line,
		Timestamp: time.Now().Unix(),
		Source:    watcher.LogSource,
		LogLevel:  "INFO", // 默认级别
		Fields: map[string]string{
			"file": watcher.FilePath,
		},
	}

	// 尝试解析日志级别
	if watcher.ParseConfig.LevelRegex != nil {
		if matches := watcher.ParseConfig.LevelRegex.FindStringSubmatch(line); len(matches) > 1 {
			logEntry.LogLevel = strings.ToUpper(matches[1])
		}
	}

	// 尝试解析时间戳
	if watcher.ParseConfig.TimeFormat != "" {
		// 这里可以添加时间解析逻辑
		// 暂时使用当前时间
		logEntry.Timestamp = time.Now().Unix()
	}

	// 解析其他字段
	if watcher.ParseConfig.FieldsRegex != nil {
		for fieldName, regex := range watcher.ParseConfig.FieldsRegex {
			if matches := regex.FindStringSubmatch(line); len(matches) > 1 {
				logEntry.Fields[fieldName] = matches[1]
			}
		}
	}

	// 应用过滤器
	if !lf.shouldForwardLog(logEntry) {
		return nil
	}

	return logEntry
}

// shouldForwardLog 检查是否应该转发日志
func (lf *LogForwarder) shouldForwardLog(logEntry *proto.LogEntry) bool {
	// 检查日志级别过滤
	if len(lf.config.LogLevelFilter) > 0 {
		levelMatched := false
		for _, allowedLevel := range lf.config.LogLevelFilter {
			if logEntry.LogLevel == allowedLevel {
				levelMatched = true
				break
			}
		}
		if !levelMatched {
			return false
		}
	}

	// 这里可以添加更多过滤逻辑
	// 例如：根据消息内容、来源等进行过滤

	return true
}

// forwardLog 转发日志到缓冲区
func (lf *LogForwarder) forwardLog(logEntry *proto.LogEntry) {
	lf.bufferMutex.Lock()
	defer lf.bufferMutex.Unlock()

	// 添加到缓冲区
	lf.buffer = append(lf.buffer, logEntry)
	lf.stats.TotalLogs++

	// 检查是否需要立即刷新
	if len(lf.buffer) >= lf.bufferSize {
		lf.flushBuffer()
	}
}

// bufferProcessor 缓冲区处理器
func (lf *LogForwarder) bufferProcessor() {
	for {
		select {
		case <-time.After(lf.config.FlushInterval):
			lf.bufferMutex.Lock()
			if len(lf.buffer) > 0 {
				lf.flushBuffer()
			}
			lf.bufferMutex.Unlock()
		case <-lf.ctx.Done():
			// 最后一次刷新
			lf.bufferMutex.Lock()
			if len(lf.buffer) > 0 {
				lf.flushBuffer()
			}
			lf.bufferMutex.Unlock()
			return
		}
	}
}

// flushBuffer 刷新缓冲区（调用时必须持有锁）
func (lf *LogForwarder) flushBuffer() {
	if len(lf.buffer) == 0 {
		return
	}

	// 发送到日志通道
	for _, logEntry := range lf.buffer {
		select {
		case lf.logChannel <- logEntry:
			lf.stats.ForwardedLogs++
		default:
			lf.stats.DroppedLogs++
			lf.logger.Warn("日志通道已满，丢弃日志", zap.String("source", logEntry.Source))
		}
	}

	lf.stats.LastForward = time.Now()
	lf.stats.BufferUsage = len(lf.buffer)

	// 清空缓冲区
	lf.buffer = lf.buffer[:0]

	lf.logger.Debug("刷新日志缓冲区",
		zap.Int64("forwarded", lf.stats.ForwardedLogs),
		zap.Int64("dropped", lf.stats.DroppedLogs))
}

// AddSystemLogWatch 添加系统日志监控
func (lf *LogForwarder) AddSystemLogWatch() error {
	// 常见系统日志路径
	systemLogPaths := []string{
		"/var/log/syslog",
		"/var/log/messages",
		"/var/log/system.log",
	}

	// 通用日志解析配置
	parseConfig := LogParseConfig{
		LevelRegex: regexp.MustCompile(`\b(DEBUG|INFO|WARN|WARNING|ERROR|FATAL)\b`),
	}

	for _, logPath := range systemLogPaths {
		if _, err := os.Stat(logPath); err == nil {
			err := lf.AddLogWatch(logPath, "system", parseConfig)
			if err != nil {
				lf.logger.Warn("添加系统日志监控失败",
					zap.String("path", logPath),
					zap.Error(err))
			}
		}
	}

	return nil
}

// AddApplicationLogWatch 添加应用日志监控
func (lf *LogForwarder) AddApplicationLogWatch(logDir, appName string) error {
	// 扫描应用日志目录
	err := filepath.Walk(logDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 检查是否为日志文件
		if info.IsDir() {
			return nil
		}

		// 检查文件扩展名
		ext := filepath.Ext(path)
		if ext != ".log" && ext != ".txt" {
			return nil
		}

		// 避免监控Agent自己的日志文件（防止递归循环）
		fileName := filepath.Base(path)
		if strings.Contains(fileName, "agent") {
			lf.logger.Debug("跳过Agent自身日志文件", zap.String("file", path))
			return nil
		}

		// 应用特定的解析配置
		parseConfig := LogParseConfig{
			LevelRegex: regexp.MustCompile(`\[(DEBUG|INFO|WARN|WARNING|ERROR|FATAL)\]`),
			FieldsRegex: map[string]*regexp.Regexp{
				"thread": regexp.MustCompile(`\[([^\]]+)\]`),
			},
		}

		return lf.AddLogWatch(path, appName, parseConfig)
	})

	return err
}

// GetStats 获取统计信息
func (lf *LogForwarder) GetStats() LogForwarderStats {
	lf.bufferMutex.Lock()
	defer lf.bufferMutex.Unlock()

	stats := lf.stats
	stats.BufferUsage = len(lf.buffer)
	return stats
}

// Stop 停止日志转发器
func (lf *LogForwarder) Stop() {
	lf.cancel()

	// 关闭所有文件监控器
	lf.mu.Lock()
	for _, watcher := range lf.watchers {
		watcher.IsActive = false
		watcher.File.Close()
	}
	lf.watchers = make(map[string]*FileWatcher)
	lf.mu.Unlock()

	// 最后一次刷新缓冲区
	lf.bufferMutex.Lock()
	if len(lf.buffer) > 0 {
		lf.flushBuffer()
	}
	lf.bufferMutex.Unlock()

	close(lf.logChannel)

	lf.logger.Info("日志转发器已停止")
}

// Start 启动日志转发器
func (lf *LogForwarder) Start(ctx context.Context) error {
	lf.logger.Info("日志转发器正在启动...")

	// 添加一些默认的日志监控
	// 添加系统日志监控
	if err := lf.AddSystemLogWatch(); err != nil {
		lf.logger.Warn("添加系统日志监控失败", zap.Error(err))
	}

	// 添加应用日志监控（如果存在的话）
	if err := lf.AddApplicationLogWatch("./logs", "agent"); err != nil {
		lf.logger.Warn("添加应用日志监控失败", zap.Error(err))
	}

	lf.logger.Info("日志转发器启动完成",
		zap.Int("active_watchers", len(lf.watchers)))

	return nil
}

// GetLogChannel 获取日志通道
func (lf *LogForwarder) GetLogChannel() chan *proto.LogEntry {
	return lf.logChannel
}

// PublishLog 发布日志到所有注册的通道
func PublishLog(logEntry *proto.LogEntry) {
	logChannelMutex.RLock()
	defer logChannelMutex.RUnlock()

	for _, ch := range logChannels {
		select {
		case ch <- logEntry:
			// 成功发送
		default:
			// 通道已满，丢弃
		}
	}
}
