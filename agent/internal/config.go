package internal

import (
	"fmt"
	"net"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"
)

// Config 包含 Agent 的配置信息
type Config struct {
	AgentID    string          // Agent 唯一标识
	AgentIP    string          // Agent IP 地址
	HostName   string          // 主机名
	StartTime  time.Time       // 启动时间
	ConfigDir  string          // 配置目录
	Collectors map[string]bool // 支持的采集器类型
}

// NewConfig 创建一个新的配置实例
func NewConfig() *Config {
	// 获取主机名
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown-host"
	}

	// 获取 IP 地址
	ip := getOutboundIP()
	if ip == "" {
		ip = "127.0.0.1"
	}

	// 如果没有指定 AgentID，则自动生成
	agentID := os.Getenv("DEVINSIGHT_AGENT_ID")
	if agentID == "" {
		agentID = fmt.Sprintf("agent-%s", uuid.New().String()[:8])
	}

	// 设置目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = "."
	}

	baseDir := fmt.Sprintf("%s/.aiops", homeDir)
	configDir := fmt.Sprintf("%s/config", baseDir)

	// 创建目录
	for _, dir := range []string{baseDir, configDir} {
		if err := os.MkdirAll(dir, 0755); err != nil {
			fmt.Printf("Error creating directory %s: %v\n", dir, err)
			// If directory creation fails, fallback to current directory
			configDir = "."
		}
	}

	// 设置支持的采集器
	collectors := map[string]bool{
		"system": true,
		"mysql":  true,
		"redis":  true,
	}

	return &Config{
		AgentID:    agentID,
		AgentIP:    ip,
		HostName:   hostname,
		StartTime:  time.Now(),
		ConfigDir:  configDir,
		Collectors: collectors,
	}
}

// GetSupportedCollectors 返回支持的采集器列表
func (c *Config) GetSupportedCollectors() []string {
	collectors := make([]string, 0, len(c.Collectors))
	for name, enabled := range c.Collectors {
		if enabled {
			collectors = append(collectors, name)
		}
	}
	return collectors
}

// getOutboundIP 获取出站 IP 地址
func getOutboundIP() string {
	// 尝试连接到公共 DNS 服务器，获取本地 IP
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return ""
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().String()
	idx := strings.LastIndex(localAddr, ":")
	if idx == -1 {
		return localAddr
	}

	return localAddr[:idx]
}
