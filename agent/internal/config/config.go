package config

import (
	"fmt"
	"net"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"
	"gopkg.in/yaml.v3"
)

// ConnectionConfig 连接配置
type ConnectionConfig struct {
	ServerAddr          string        `yaml:"server_addr"`
	InitialBackoff      time.Duration `yaml:"initial_backoff"`
	MaxBackoff          time.Duration `yaml:"max_backoff"`
	BackoffMultiplier   float64       `yaml:"backoff_multiplier"`
	MaxRetries          int           `yaml:"max_retries"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval"`
	ConnectTimeout      time.Duration `yaml:"connect_timeout"`
}

// CollectorRetryConfig 重试配置
type CollectorRetryConfig struct {
	MaxRetries     int           `yaml:"max_retries"`
	InitialBackoff time.Duration `yaml:"initial_backoff"`
	MaxBackoff     time.Duration `yaml:"max_backoff"`
	BackoffFactor  float64       `yaml:"backoff_factor"`
}

// CollectorHealthCheckConfig 健康检查配置
type CollectorHealthCheckConfig struct {
	Interval         time.Duration `yaml:"interval"`
	Timeout          time.Duration `yaml:"timeout"`
	FailureThreshold int           `yaml:"failure_threshold"`
}

// CollectorResourceLimitsConfig 资源限制配置
type CollectorResourceLimitsConfig struct {
	MaxMemoryMB     int64 `yaml:"max_memory_mb"`
	MaxGoroutines   int   `yaml:"max_goroutines"`
	MaxMetricsQueue int   `yaml:"max_metrics_queue"`
}

// CollectorConfig 采集器配置
type CollectorConfig struct {
	Retry          CollectorRetryConfig          `yaml:"retry"`
	HealthCheck    CollectorHealthCheckConfig    `yaml:"health_check"`
	ResourceLimits CollectorResourceLimitsConfig `yaml:"resource_limits"`
}

// SystemLogsConfig 系统日志配置
type SystemLogsConfig struct {
	Enabled bool     `yaml:"enabled"`
	Paths   []string `yaml:"paths"`
}

// ApplicationLogsConfig 应用日志配置
type ApplicationLogsConfig struct {
	Enabled     bool     `yaml:"enabled"`
	Directories []string `yaml:"directories"`
}

// LogForwarderConfig 日志转发器配置
type LogForwarderConfig struct {
	Enabled         bool                  `yaml:"enabled"`
	BufferSize      int                   `yaml:"buffer_size"`
	FlushInterval   time.Duration         `yaml:"flush_interval"`
	ScanInterval    time.Duration         `yaml:"scan_interval"`
	MaxFiles        int                   `yaml:"max_files"`
	MaxLineLength   int                   `yaml:"max_line_length"`
	IncludePatterns []string              `yaml:"include_patterns"`
	ExcludePatterns []string              `yaml:"exclude_patterns"`
	LogLevelFilter  []string              `yaml:"log_level_filter"`
	SystemLogs      SystemLogsConfig      `yaml:"system_logs"`
	ApplicationLogs ApplicationLogsConfig `yaml:"application_logs"`
}

// AgentConfig Agent基本配置
type AgentConfig struct {
	AgentID                 string   `yaml:"agent_id"`
	AgentIP                 string   `yaml:"agent_ip"`
	SupportedCollectorTypes []string `yaml:"supported_collector_types"`
}

// Config 包含 Agent 的完整配置信息
type Config struct {
	// 基本信息
	AgentID                 string    // Agent 唯一标识
	AgentIP                 string    // Agent IP 地址
	HostName                string    // 主机名
	StartTime               time.Time // 启动时间
	ConfigDir               string    // 配置目录
	DataDir                 string    // 数据目录
	LogDir                  string    // 日志目录
	LogFile                 string    // 日志文件路径
	SupportedCollectorTypes []string  // 支持的采集器类型

	// YAML配置文件内容
	Agent        AgentConfig        `yaml:"agent"`
	Connection   ConnectionConfig   `yaml:"connection"`
	Collector    CollectorConfig    `yaml:"collector"`
	LogForwarder LogForwarderConfig `yaml:"log_forwarder"`
}

// New 创建一个新的 Config 实例
func New() *Config {
	return NewWithFile("config/agent_config.yaml")
}

// NewWithFile 从文件创建配置
func NewWithFile(configFile string) *Config {
	// 生成唯一的 Agent ID
	id := uuid.New().String()

	// 获取主机名
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}

	// 获取 IP 地址
	ip := getOutboundIP()

	// 创建目录
	configDir := "./config"
	if err := os.MkdirAll(configDir, 0755); err != nil {
		// 如果创建目录失败，使用当前目录
		configDir = "."
	}

	config := &Config{
		AgentID:                 id,
		AgentIP:                 ip,
		HostName:                hostname,
		StartTime:               time.Now(),
		ConfigDir:               configDir,
		SupportedCollectorTypes: []string{"system", "mysql", "redis", "webapi"},
	}

	// 尝试加载配置文件
	if err := config.LoadFromFile(configFile); err != nil {
		// 如果加载失败，使用默认配置
		config.setDefaults()
	}

	// 如果配置文件中没有设置AgentID，使用生成的ID
	if config.Agent.AgentID != "" {
		config.AgentID = config.Agent.AgentID
	} else {
		config.Agent.AgentID = config.AgentID
	}

	// 如果配置文件中没有设置AgentIP，使用检测的IP
	if config.Agent.AgentIP != "" {
		config.AgentIP = config.Agent.AgentIP
	} else {
		config.Agent.AgentIP = config.AgentIP
	}

	// 使用配置文件中的支持类型
	if len(config.Agent.SupportedCollectorTypes) > 0 {
		config.SupportedCollectorTypes = config.Agent.SupportedCollectorTypes
	}

	return config
}

// LoadFromFile 从文件加载配置
func (c *Config) LoadFromFile(filename string) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 创建一个临时结构来解析完整的YAML
	var yamlConfig struct {
		Agent        AgentConfig        `yaml:"agent"`
		Connection   ConnectionConfig   `yaml:"connection"`
		Collector    CollectorConfig    `yaml:"collector"`
		LogForwarder LogForwarderConfig `yaml:"log_forwarder"`
	}

	if err := yaml.Unmarshal(data, &yamlConfig); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	c.Agent = yamlConfig.Agent
	c.Connection = yamlConfig.Connection
	c.Collector = yamlConfig.Collector
	c.LogForwarder = yamlConfig.LogForwarder

	return nil
}

// setDefaults 设置默认配置
func (c *Config) setDefaults() {
	// 连接配置默认值
	c.Connection = ConnectionConfig{
		ServerAddr:          "localhost:50051",
		InitialBackoff:      time.Second,
		MaxBackoff:          time.Minute * 5,
		BackoffMultiplier:   2.0,
		MaxRetries:          -1,
		HealthCheckInterval: time.Second * 30,
		ConnectTimeout:      time.Second * 10,
	}

	// 采集器配置默认值
	c.Collector = CollectorConfig{
		Retry: CollectorRetryConfig{
			MaxRetries:     3,
			InitialBackoff: time.Second,
			MaxBackoff:     time.Minute,
			BackoffFactor:  2.0,
		},
		HealthCheck: CollectorHealthCheckConfig{
			Interval:         time.Second * 30,
			Timeout:          time.Second * 10,
			FailureThreshold: 3,
		},
		ResourceLimits: CollectorResourceLimitsConfig{
			MaxMemoryMB:     512,
			MaxGoroutines:   100,
			MaxMetricsQueue: 1000,
		},
	}

	// 日志转发器配置默认值
	c.LogForwarder = LogForwarderConfig{
		Enabled:         true,
		BufferSize:      100,
		FlushInterval:   time.Second * 5,
		ScanInterval:    time.Second,
		MaxFiles:        50,
		MaxLineLength:   4096,
		IncludePatterns: []string{"*.log", "*.txt"},
		ExcludePatterns: []string{"*.tmp", "*.backup"},
		LogLevelFilter:  []string{"DEBUG", "INFO", "WARN", "ERROR"},
		SystemLogs: SystemLogsConfig{
			Enabled: true,
			Paths:   []string{"/var/log/syslog", "/var/log/messages", "/var/log/system.log"},
		},
		ApplicationLogs: ApplicationLogsConfig{
			Enabled:     true,
			Directories: []string{"./logs", "/var/log/app"},
		},
	}

	// Agent配置默认值
	c.Agent = AgentConfig{
		AgentID:                 c.AgentID,
		AgentIP:                 c.AgentIP,
		SupportedCollectorTypes: []string{"system", "mysql", "redis", "webapi"},
	}
}

// getOutboundIP 获取本机的出站 IP 地址
func getOutboundIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

// GetSupportedCollectorTypes 获取支持的采集器类型列表
func (c *Config) GetSupportedCollectorTypes() []string {
	return c.SupportedCollectorTypes
}

// SaveConfig 保存配置到文件
func (c *Config) SaveConfig() error {
	configFile := fmt.Sprintf("%s/agent.conf", c.ConfigDir)

	// 简单配置保存，实际可能使用 JSON 或 YAML 格式
	content := fmt.Sprintf("agent_id=%s\n", c.AgentID)
	content += fmt.Sprintf("agent_ip=%s\n", c.AgentIP)
	content += fmt.Sprintf("host_name=%s\n", c.HostName)
	content += fmt.Sprintf("start_time=%s\n", c.StartTime.Format(time.RFC3339))
	content += fmt.Sprintf("supported_collectors=%s\n", strings.Join(c.SupportedCollectorTypes, ","))

	return os.WriteFile(configFile, []byte(content), 0644)
}

// GetConnectionConfig 获取连接配置
func (c *Config) GetConnectionConfig() ConnectionConfig {
	return c.Connection
}

// GetCollectorConfig 获取采集器配置
func (c *Config) GetCollectorConfig() CollectorConfig {
	return c.Collector
}

// GetLogForwarderConfig 获取日志转发器配置
func (c *Config) GetLogForwarderConfig() LogForwarderConfig {
	return c.LogForwarder
}

// IsLogForwarderEnabled 检查是否启用日志转发
func (c *Config) IsLogForwarderEnabled() bool {
	return c.LogForwarder.Enabled
}

// GetDeviceID 获取设备ID（用于日志转发）
func (c *Config) GetDeviceID() string {
	// 使用AgentID作为设备ID
	return c.AgentID
}
