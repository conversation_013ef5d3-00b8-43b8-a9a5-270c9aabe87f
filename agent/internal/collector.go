package internal

import (
	"aiops/pkg/log"
	"aiops/pkg/proto"
	"context"
	"errors"
	"fmt"
	"math"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries     int           // 最大重试次数
	InitialBackoff time.Duration // 初始退避时间
	MaxBackoff     time.Duration // 最大退避时间
	BackoffFactor  float64       // 退避倍数
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Interval         time.Duration // 检查间隔
	Timeout          time.Duration // 超时时间
	FailureThreshold int           // 失败阈值
}

// ResourceLimits 资源限制配置
type ResourceLimits struct {
	MaxMemoryMB     int64 // 最大内存限制(MB)
	MaxGoroutines   int   // 最大协程数
	MaxMetricsQueue int   // 最大指标队列长度
}

// TaskPriority 任务优先级
type TaskPriority int

const (
	PriorityLow      TaskPriority = 1
	PriorityNormal   TaskPriority = 2
	PriorityHigh     TaskPriority = 3
	PriorityCritical TaskPriority = 4
)

// QueuedTask 排队的任务
type QueuedTask struct {
	Config         *proto.CollectorTaskConfig
	Priority       TaskPriority
	StatusCallback func(*proto.TaskStatus)
	QueuedAt       time.Time
	RetryCount     int
}

// CollectorManagerStats 采集器管理器统计信息
type CollectorManagerStats struct {
	TotalCollectors    int64 // 总采集器数
	ActiveCollectors   int64 // 活跃采集器数
	FailedCollectors   int64 // 失败采集器数
	TotalMetrics       int64 // 总指标数
	FailedMetrics      int64 // 失败指标数
	LastHealthCheck    time.Time
	HealthChecksPassed int64
	HealthChecksFailed int64
}

// CollectorHealth 采集器健康状态
type CollectorHealth struct {
	TaskID              string
	IsHealthy           bool
	LastCheck           time.Time
	ConsecutiveFailures int
	LastError           error
}

// CollectorManager 管理所有运行中的采集器
type CollectorManager struct {
	collectors map[string]*Collector
	metrics    chan *proto.MetricData
	logger     *log.Logger
	mu         sync.RWMutex

	// 健壮性增强字段
	ctx            context.Context
	cancel         context.CancelFunc
	retryConfig    RetryConfig
	healthConfig   HealthCheckConfig
	resourceLimits ResourceLimits

	// 统计信息
	stats CollectorManagerStats

	// 任务队列
	taskQueue    chan *QueuedTask
	healthTicker *time.Ticker

	// 资源监控
	memoryStats      *runtime.MemStats
	goroutineCount   int64
	currentQueueSize int64

	// 健康状态
	healthStatus map[string]*CollectorHealth
}

// Collector 表示一个采集器实例
type Collector struct {
	TaskID         string
	DeviceID       string
	DeviceType     string
	Config         *proto.CollectorTaskConfig
	StopChan       chan struct{}
	StatusCallback func(*proto.TaskStatus)

	// 健壮性增强字段
	ctx                 context.Context
	cancel              context.CancelFunc
	consecutiveFailures int64
	lastSuccessTime     time.Time
	lastError           error
	isHealthy           bool
	priority            TaskPriority

	// 统计信息
	totalCollections      int64
	successfulCollections int64
	failedCollections     int64
}

// NewCollectorManager 创建一个新的采集器管理器
func NewCollectorManager(logger *log.Logger) *CollectorManager {
	metrics := make(chan *proto.MetricData, 100)

	// 注册指标通道
	RegisterMetricChannel(metrics)

	ctx, cancel := context.WithCancel(context.Background())

	// 默认配置
	retryConfig := RetryConfig{
		MaxRetries:     3,
		InitialBackoff: 1 * time.Second,
		MaxBackoff:     30 * time.Second,
		BackoffFactor:  2.0,
	}

	healthConfig := HealthCheckConfig{
		Interval:         30 * time.Second,
		Timeout:          10 * time.Second,
		FailureThreshold: 3,
	}

	resourceLimits := ResourceLimits{
		MaxMemoryMB:     512, // 512MB
		MaxGoroutines:   100,
		MaxMetricsQueue: 1000,
	}

	manager := &CollectorManager{
		collectors:     make(map[string]*Collector),
		metrics:        metrics,
		logger:         logger,
		mu:             sync.RWMutex{},
		ctx:            ctx,
		cancel:         cancel,
		retryConfig:    retryConfig,
		healthConfig:   healthConfig,
		resourceLimits: resourceLimits,
		taskQueue:      make(chan *QueuedTask, 100),
		memoryStats:    &runtime.MemStats{},
		healthStatus:   make(map[string]*CollectorHealth),
	}

	// 启动后台服务
	go manager.taskQueueProcessor()
	go manager.healthCheckProcessor()
	go manager.resourceMonitor()

	return manager
}

// StartCollector 启动一个采集器（异步，支持优先级和队列）
func (m *CollectorManager) StartCollector(config *proto.CollectorTaskConfig, statusCallback func(*proto.TaskStatus)) {
	m.StartCollectorWithPriority(config, statusCallback, PriorityNormal)
}

// StartCollectorWithPriority 按优先级启动采集器
func (m *CollectorManager) StartCollectorWithPriority(config *proto.CollectorTaskConfig, statusCallback func(*proto.TaskStatus), priority TaskPriority) {
	task := &QueuedTask{
		Config:         config,
		Priority:       priority,
		StatusCallback: statusCallback,
		QueuedAt:       time.Now(),
	}

	// 检查队列是否已满
	select {
	case m.taskQueue <- task:
		atomic.AddInt64(&m.currentQueueSize, 1)
		m.logger.Info("采集器任务已加入队列",
			zap.String("taskID", config.TaskId),
			zap.Int("priority", int(priority)))
	default:
		m.logger.Error("任务队列已满，采集器启动失败", zap.String("taskID", config.TaskId))
		if statusCallback != nil {
			statusCallback(&proto.TaskStatus{
				TaskId:       config.TaskId,
				Status:       "failed",
				ErrorMessage: "任务队列已满",
			})
		}
	}
}

// startCollectorInternal 内部启动采集器方法
func (m *CollectorManager) startCollectorInternal(config *proto.CollectorTaskConfig, statusCallback func(*proto.TaskStatus), priority TaskPriority) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	taskID := config.TaskId

	// 检查是否已存在相同 ID 的采集器
	if existingCollector, exists := m.collectors[taskID]; exists {
		// 发送停止信号
		existingCollector.cancel()
		m.logger.Info("停止现有采集器", zap.String("taskID", taskID))
		delete(m.collectors, taskID)
		delete(m.healthStatus, taskID)
	}

	// 创建采集器上下文
	ctx, cancel := context.WithCancel(m.ctx)

	// 创建采集器
	collector := &Collector{
		TaskID:          taskID,
		DeviceID:        config.DeviceId,
		DeviceType:      config.DeviceType,
		Config:          config,
		StopChan:        make(chan struct{}),
		StatusCallback:  statusCallback,
		ctx:             ctx,
		cancel:          cancel,
		lastSuccessTime: time.Now(),
		isHealthy:       true,
		priority:        priority,
	}

	m.collectors[taskID] = collector
	atomic.AddInt64(&m.stats.TotalCollectors, 1)
	atomic.AddInt64(&m.stats.ActiveCollectors, 1)

	// 启动采集任务
	go m.runCollectorRobust(collector)

	m.logger.Info("启动采集器",
		zap.String("taskID", taskID),
		zap.String("设备类型", config.DeviceType),
		zap.Int("priority", int(priority)))

	return nil
}

// StopCollector 停止一个采集器
func (m *CollectorManager) StopCollector(taskID string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	collector, exists := m.collectors[taskID]
	if !exists {
		m.logger.Info("采集器未找到", zap.String("taskID", taskID))
		return
	}

	// 发送停止信号
	collector.cancel()
	close(collector.StopChan)
	delete(m.collectors, taskID)
	delete(m.healthStatus, taskID)

	atomic.AddInt64(&m.stats.ActiveCollectors, -1)

	m.logger.Info("停止采集器", zap.String("taskID", taskID))

	// 发送停止状态
	if collector.StatusCallback != nil {
		collector.StatusCallback(&proto.TaskStatus{
			TaskId:               taskID,
			Status:               "stopped",
			LastCollectTimestamp: time.Now().Unix(),
		})
	}
}

// StopAll 停止所有采集器
func (m *CollectorManager) StopAll() {
	m.mu.Lock()
	defer m.mu.Unlock()

	for taskID, collector := range m.collectors {
		collector.cancel()
		close(collector.StopChan)
		m.logger.Info("停止采集器", zap.String("taskID", taskID))
	}

	m.collectors = make(map[string]*Collector)
	m.healthStatus = make(map[string]*CollectorHealth)

	// 停止后台服务
	m.cancel()
	if m.healthTicker != nil {
		m.healthTicker.Stop()
	}
}

// runCollectorRobust 运行采集器主循环（健壮版本）
func (m *CollectorManager) runCollectorRobust(collector *Collector) {
	defer func() {
		if r := recover(); r != nil {
			m.logger.Error("采集器运行时发生panic",
				zap.String("taskID", collector.TaskID),
				zap.Any("panic", r))

			// 记录为不健康
			collector.isHealthy = false
			atomic.AddInt64(&collector.consecutiveFailures, 1)
		}

		// 清理资源
		atomic.AddInt64(&m.stats.ActiveCollectors, -1)
	}()

	// 获取频率，默认 60 秒
	frequency := time.Duration(collector.Config.FrequencySeconds) * time.Second
	if frequency <= 0 {
		frequency = 60 * time.Second
	}

	// 创建定时器
	ticker := time.NewTicker(frequency)
	defer ticker.Stop()

	// 首次立即采集
	m.collectOnceRobust(collector)

	// 循环采集
	for {
		select {
		case <-ticker.C:
			m.collectOnceRobust(collector)
		case <-collector.StopChan:
			m.logger.Info("采集器收到停止信号", zap.String("taskID", collector.TaskID))
			return
		case <-collector.ctx.Done():
			m.logger.Info("采集器上下文已取消", zap.String("taskID", collector.TaskID))
			return
		}
	}
}

// collectOnceRobust 执行一次采集（健壮版本）
func (m *CollectorManager) collectOnceRobust(collector *Collector) {
	defer func() {
		if r := recover(); r != nil {
			m.logger.Error("采集过程发生panic",
				zap.String("taskID", collector.TaskID),
				zap.Any("panic", r))

			atomic.AddInt64(&collector.failedCollections, 1)
			atomic.AddInt64(&collector.consecutiveFailures, 1)
			atomic.AddInt64(&m.stats.FailedMetrics, 1)
			collector.isHealthy = false
		}
	}()

	atomic.AddInt64(&collector.totalCollections, 1)

	m.logger.Debug("执行采集",
		zap.String("taskID", collector.TaskID),
		zap.String("设备", collector.DeviceID))

	// 更新状态为 running
	if collector.StatusCallback != nil {
		collector.StatusCallback(&proto.TaskStatus{
			TaskId:               collector.TaskID,
			Status:               "running",
			LastCollectTimestamp: time.Now().Unix(),
		})
	}

	// 创建采集上下文，设置超时
	ctx, cancel := context.WithTimeout(collector.ctx, 30*time.Second)
	defer cancel()

	// 根据设备类型选择采集方式
	var err error

	switch collector.DeviceType {
	case "mysql":
		err = m.collectMySQLRobust(ctx, collector)
	case "redis":
		err = m.collectRedisRobust(ctx, collector)
	default:
		err = m.collectSystemRobust(ctx, collector)
	}

	// 处理采集结果
	now := time.Now().Unix()
	status := "success"
	errorMsg := ""

	if err != nil {
		status = "failed"
		errorMsg = err.Error()
		atomic.AddInt64(&collector.failedCollections, 1)
		atomic.AddInt64(&collector.consecutiveFailures, 1)
		atomic.AddInt64(&m.stats.FailedMetrics, 1)
		collector.lastError = err
		collector.isHealthy = false

		m.logger.Error("采集失败",
			zap.String("taskID", collector.TaskID),
			zap.Error(err))
	} else {
		atomic.AddInt64(&collector.successfulCollections, 1)
		atomic.StoreInt64(&collector.consecutiveFailures, 0)
		atomic.AddInt64(&m.stats.TotalMetrics, 1)
		collector.lastSuccessTime = time.Now()
		collector.lastError = nil
		collector.isHealthy = true
	}

	// 更新状态
	if collector.StatusCallback != nil {
		collector.StatusCallback(&proto.TaskStatus{
			TaskId:               collector.TaskID,
			Status:               status,
			ErrorMessage:         errorMsg,
			LastCollectTimestamp: now,
		})
	}
}

// collectSystemRobust 采集系统指标（健壮版本）
func (m *CollectorManager) collectSystemRobust(ctx context.Context, collector *Collector) error {
	deviceID := collector.Config.DeviceId
	now := time.Now().Unix()

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// 模拟 CPU 使用率
	cpuMetric := &proto.MetricData{
		DeviceId:  deviceID,
		MetricKey: "system.cpu.usage",
		ValueType: &proto.MetricData_NumericValue{NumericValue: float64(50 + time.Now().UnixNano()%30)}, // 50% - 80%
		Timestamp: now,
		Labels:    map[string]string{"type": "system", "unit": "percent"},
	}

	// 模拟内存使用率
	memMetric := &proto.MetricData{
		DeviceId:  deviceID,
		MetricKey: "system.memory.usage",
		ValueType: &proto.MetricData_NumericValue{NumericValue: float64(60 + time.Now().UnixNano()%25)}, // 60% - 85%
		Timestamp: now,
		Labels:    map[string]string{"type": "system", "unit": "percent"},
	}

	// 模拟磁盘使用率
	diskMetric := &proto.MetricData{
		DeviceId:  deviceID,
		MetricKey: "system.disk.usage",
		ValueType: &proto.MetricData_NumericValue{NumericValue: float64(40 + time.Now().UnixNano()%40)}, // 40% - 80%
		Timestamp: now,
		Labels:    map[string]string{"type": "system", "unit": "percent"},
	}

	// 安全发送指标
	metrics := []*proto.MetricData{cpuMetric, memMetric, diskMetric}
	return m.safelySendMetrics(ctx, metrics)
}

// collectMySQLRobust 采集 MySQL 指标（健壮版本）
func (m *CollectorManager) collectMySQLRobust(ctx context.Context, collector *Collector) error {
	deviceID := collector.Config.DeviceId
	now := time.Now().Unix()

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// 模拟连接数
	connectionsMetric := &proto.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.connections",
		ValueType: &proto.MetricData_NumericValue{NumericValue: float64(100 + time.Now().UnixNano()%150)}, // 100 - 250
		Timestamp: now,
		Labels:    map[string]string{"type": "mysql", "unit": "connections"},
	}

	// 模拟查询率
	queriesMetric := &proto.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.queries_per_second",
		ValueType: &proto.MetricData_NumericValue{NumericValue: float64(200 + time.Now().UnixNano()%300)}, // 200 - 500
		Timestamp: now,
		Labels:    map[string]string{"type": "mysql", "unit": "queries/s"},
	}

	// 模拟慢查询数
	slowQueriesMetric := &proto.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.slow_queries",
		ValueType: &proto.MetricData_NumericValue{NumericValue: float64(0 + time.Now().UnixNano()%10)}, // 0 - 10
		Timestamp: now,
		Labels:    map[string]string{"type": "mysql", "unit": "queries"},
	}

	// 安全发送指标
	metrics := []*proto.MetricData{connectionsMetric, queriesMetric, slowQueriesMetric}
	return m.safelySendMetrics(ctx, metrics)
}

// collectRedisRobust 采集 Redis 指标（健壮版本）
func (m *CollectorManager) collectRedisRobust(ctx context.Context, collector *Collector) error {
	deviceID := collector.Config.DeviceId
	now := time.Now().Unix()

	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// 模拟内存使用
	memoryMetric := &proto.MetricData{
		DeviceId:  deviceID,
		MetricKey: "redis.memory_usage",
		ValueType: &proto.MetricData_NumericValue{NumericValue: float64(100 + time.Now().UnixNano()%400)}, // 100MB - 500MB
		Timestamp: now,
		Labels:    map[string]string{"type": "redis", "unit": "MB"},
	}

	// 模拟客户端连接数
	connectionsMetric := &proto.MetricData{
		DeviceId:  deviceID,
		MetricKey: "redis.connections",
		ValueType: &proto.MetricData_NumericValue{NumericValue: float64(50 + time.Now().UnixNano()%100)}, // 50 - 150
		Timestamp: now,
		Labels:    map[string]string{"type": "redis", "unit": "connections"},
	}

	// 模拟命令执行率
	commandsMetric := &proto.MetricData{
		DeviceId:  deviceID,
		MetricKey: "redis.commands_per_second",
		ValueType: &proto.MetricData_NumericValue{NumericValue: float64(500 + time.Now().UnixNano()%1500)}, // 500 - 2000
		Timestamp: now,
		Labels:    map[string]string{"type": "redis", "unit": "commands/s"},
	}

	// 安全发送指标
	metrics := []*proto.MetricData{memoryMetric, connectionsMetric, commandsMetric}
	return m.safelySendMetrics(ctx, metrics)
}

// safelySendMetrics 安全发送指标到通道
func (m *CollectorManager) safelySendMetrics(ctx context.Context, metrics []*proto.MetricData) error {
	for _, metric := range metrics {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case m.metrics <- metric:
			// 成功发送
		case <-time.After(5 * time.Second):
			return errors.New("发送指标超时")
		}
	}
	return nil
}

// 指标通道注册
var (
	metricChannels = make([]chan *proto.MetricData, 0)
	metricMutex    sync.RWMutex
)

// RegisterMetricChannel 注册一个指标通道
func RegisterMetricChannel(ch chan *proto.MetricData) {
	metricMutex.Lock()
	defer metricMutex.Unlock()

	metricChannels = append(metricChannels, ch)
}

// PublishMetric 发布指标到所有注册的通道
func PublishMetric(metric *proto.MetricData) {
	metricMutex.RLock()
	defer metricMutex.RUnlock()

	for _, ch := range metricChannels {
		select {
		case ch <- metric:
			// 成功发送
		default:
			// 通道已满，丢弃
		}
	}
}

// taskQueueProcessor 处理任务队列
func (m *CollectorManager) taskQueueProcessor() {
	for {
		select {
		case task := <-m.taskQueue:
			atomic.AddInt64(&m.currentQueueSize, -1)

			// 检查资源限制
			if !m.checkResourceLimits() {
				m.logger.Warn("资源限制达到，跳过任务", zap.String("taskID", task.Config.TaskId))
				m.rejectTask(task, "资源限制")
				continue
			}

			// 处理任务（带重试）
			m.processTaskWithRetry(task)

		case <-m.ctx.Done():
			return
		}
	}
}

// healthCheckProcessor 健康检查处理器
func (m *CollectorManager) healthCheckProcessor() {
	m.healthTicker = time.NewTicker(m.healthConfig.Interval)
	defer m.healthTicker.Stop()

	for {
		select {
		case <-m.healthTicker.C:
			m.performHealthChecks()
		case <-m.ctx.Done():
			return
		}
	}
}

// resourceMonitor 资源监控器
func (m *CollectorManager) resourceMonitor() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.updateResourceStats()
			m.enforceResourceLimits()
		case <-m.ctx.Done():
			return
		}
	}
}

// checkResourceLimits 检查资源限制
func (m *CollectorManager) checkResourceLimits() bool {
	runtime.ReadMemStats(m.memoryStats)

	// 检查内存限制
	currentMemoryMB := int64(m.memoryStats.Alloc) / 1024 / 1024
	if currentMemoryMB > m.resourceLimits.MaxMemoryMB {
		m.logger.Warn("内存使用超出限制",
			zap.Int64("current", currentMemoryMB),
			zap.Int64("limit", m.resourceLimits.MaxMemoryMB))
		return false
	}

	// 检查协程数限制
	currentGoroutines := int64(runtime.NumGoroutine())
	atomic.StoreInt64(&m.goroutineCount, currentGoroutines)
	if currentGoroutines > int64(m.resourceLimits.MaxGoroutines) {
		m.logger.Warn("协程数超出限制",
			zap.Int64("current", currentGoroutines),
			zap.Int("limit", m.resourceLimits.MaxGoroutines))
		return false
	}

	// 检查指标队列长度
	queueSize := atomic.LoadInt64(&m.currentQueueSize)
	if queueSize > int64(m.resourceLimits.MaxMetricsQueue) {
		m.logger.Warn("任务队列超出限制",
			zap.Int64("current", queueSize),
			zap.Int("limit", m.resourceLimits.MaxMetricsQueue))
		return false
	}

	return true
}

// processTaskWithRetry 带重试的任务处理
func (m *CollectorManager) processTaskWithRetry(task *QueuedTask) {
	var lastError error

	for attempt := 0; attempt <= m.retryConfig.MaxRetries; attempt++ {
		if attempt > 0 {
			// 计算退避时间
			backoff := m.calculateBackoff(attempt)
			m.logger.Info("任务重试",
				zap.String("taskID", task.Config.TaskId),
				zap.Int("attempt", attempt),
				zap.Duration("backoff", backoff))

			select {
			case <-time.After(backoff):
			case <-m.ctx.Done():
				return
			}
		}

		// 尝试启动采集器
		err := m.startCollectorInternal(task.Config, task.StatusCallback, task.Priority)
		if err == nil {
			m.logger.Info("任务处理成功",
				zap.String("taskID", task.Config.TaskId),
				zap.Int("attempts", attempt+1))
			return
		}

		lastError = err
		task.RetryCount++

		m.logger.Warn("任务处理失败",
			zap.String("taskID", task.Config.TaskId),
			zap.Int("attempt", attempt+1),
			zap.Error(err))
	}

	// 所有重试都失败了
	m.logger.Error("任务处理最终失败",
		zap.String("taskID", task.Config.TaskId),
		zap.Int("totalAttempts", m.retryConfig.MaxRetries+1),
		zap.Error(lastError))

	atomic.AddInt64(&m.stats.FailedCollectors, 1)
	m.rejectTask(task, fmt.Sprintf("重试失败: %v", lastError))
}

// calculateBackoff 计算退避时间
func (m *CollectorManager) calculateBackoff(attempt int) time.Duration {
	backoff := float64(m.retryConfig.InitialBackoff) * math.Pow(m.retryConfig.BackoffFactor, float64(attempt-1))
	maxBackoff := float64(m.retryConfig.MaxBackoff)

	if backoff > maxBackoff {
		backoff = maxBackoff
	}

	return time.Duration(backoff)
}

// rejectTask 拒绝任务
func (m *CollectorManager) rejectTask(task *QueuedTask, reason string) {
	if task.StatusCallback != nil {
		task.StatusCallback(&proto.TaskStatus{
			TaskId:       task.Config.TaskId,
			Status:       "failed",
			ErrorMessage: fmt.Sprintf("任务被拒绝: %s", reason),
		})
	}
}

// performHealthChecks 执行健康检查
func (m *CollectorManager) performHealthChecks() {
	m.mu.RLock()
	collectors := make(map[string]*Collector)
	for k, v := range m.collectors {
		collectors[k] = v
	}
	m.mu.RUnlock()

	for taskID, collector := range collectors {
		go m.checkCollectorHealth(taskID, collector)
	}

	m.stats.LastHealthCheck = time.Now()
}

// checkCollectorHealth 检查单个采集器健康状态
func (m *CollectorManager) checkCollectorHealth(taskID string, collector *Collector) {
	ctx, cancel := context.WithTimeout(m.ctx, m.healthConfig.Timeout)
	defer cancel()

	health := m.getOrCreateHealthStatus(taskID)

	// 简单的健康检查：检查采集器是否响应
	select {
	case <-ctx.Done():
		// 超时，认为不健康
		m.recordHealthCheckFailure(health)
	default:
		// 检查采集器的统计信息
		if m.isCollectorResponsive(collector) {
			m.recordHealthCheckSuccess(health)
		} else {
			m.recordHealthCheckFailure(health)
		}
	}

	// 如果连续失败达到阈值，重启采集器
	if health.ConsecutiveFailures >= m.healthConfig.FailureThreshold {
		m.logger.Warn("采集器连续健康检查失败，准备重启",
			zap.String("taskID", taskID),
			zap.Int("failures", health.ConsecutiveFailures))

		m.restartCollector(taskID, collector)
	}
}

// isCollectorResponsive 检查采集器是否响应
func (m *CollectorManager) isCollectorResponsive(collector *Collector) bool {
	// 检查最近是否有成功采集
	timeSinceLastSuccess := time.Since(collector.lastSuccessTime)
	maxIdleTime := time.Duration(collector.Config.FrequencySeconds) * time.Second * 2 // 允许2个采集周期

	return timeSinceLastSuccess < maxIdleTime && collector.isHealthy
}

// getOrCreateHealthStatus 获取或创建健康状态
func (m *CollectorManager) getOrCreateHealthStatus(taskID string) *CollectorHealth {
	m.mu.Lock()
	defer m.mu.Unlock()

	if health, exists := m.healthStatus[taskID]; exists {
		return health
	}

	health := &CollectorHealth{
		TaskID:    taskID,
		IsHealthy: true,
		LastCheck: time.Now(),
	}
	m.healthStatus[taskID] = health
	return health
}

// recordHealthCheckSuccess 记录健康检查成功
func (m *CollectorManager) recordHealthCheckSuccess(health *CollectorHealth) {
	health.IsHealthy = true
	health.LastCheck = time.Now()
	health.ConsecutiveFailures = 0
	health.LastError = nil
	atomic.AddInt64(&m.stats.HealthChecksPassed, 1)
}

// recordHealthCheckFailure 记录健康检查失败
func (m *CollectorManager) recordHealthCheckFailure(health *CollectorHealth) {
	health.IsHealthy = false
	health.LastCheck = time.Now()
	health.ConsecutiveFailures++
	atomic.AddInt64(&m.stats.HealthChecksFailed, 1)
}

// restartCollector 重启采集器
func (m *CollectorManager) restartCollector(taskID string, collector *Collector) {
	m.logger.Info("重启采集器", zap.String("taskID", taskID))

	// 停止现有采集器
	m.StopCollector(taskID)

	// 等待一小段时间
	time.Sleep(2 * time.Second)

	// 重新启动采集器
	task := &QueuedTask{
		Config:         collector.Config,
		Priority:       collector.priority,
		StatusCallback: collector.StatusCallback,
		QueuedAt:       time.Now(),
	}

	select {
	case m.taskQueue <- task:
		atomic.AddInt64(&m.currentQueueSize, 1)
		m.logger.Info("采集器重启任务已加入队列", zap.String("taskID", taskID))
	default:
		m.logger.Error("任务队列已满，无法重启采集器", zap.String("taskID", taskID))
	}
}

// updateResourceStats 更新资源统计
func (m *CollectorManager) updateResourceStats() {
	runtime.ReadMemStats(m.memoryStats)
	atomic.StoreInt64(&m.goroutineCount, int64(runtime.NumGoroutine()))

	m.mu.RLock()
	activeCollectors := int64(len(m.collectors))
	m.mu.RUnlock()

	atomic.StoreInt64(&m.stats.ActiveCollectors, activeCollectors)
}

// enforceResourceLimits 强制执行资源限制
func (m *CollectorManager) enforceResourceLimits() {
	// 如果内存使用过高，停止一些低优先级的采集器
	currentMemoryMB := int64(m.memoryStats.Alloc) / 1024 / 1024
	if currentMemoryMB > m.resourceLimits.MaxMemoryMB {
		m.stopLowPriorityCollectors(1)
	}

	// 如果协程数过多，停止一些采集器
	if atomic.LoadInt64(&m.goroutineCount) > int64(m.resourceLimits.MaxGoroutines) {
		m.stopLowPriorityCollectors(2)
	}
}

// stopLowPriorityCollectors 停止低优先级采集器
func (m *CollectorManager) stopLowPriorityCollectors(count int) {
	m.mu.RLock()
	var lowPriorityCollectors []string

	for taskID, collector := range m.collectors {
		if collector.priority <= PriorityNormal {
			lowPriorityCollectors = append(lowPriorityCollectors, taskID)
		}
	}
	m.mu.RUnlock()

	// 停止指定数量的低优先级采集器
	for i := 0; i < count && i < len(lowPriorityCollectors); i++ {
		taskID := lowPriorityCollectors[i]
		m.logger.Warn("由于资源限制停止低优先级采集器", zap.String("taskID", taskID))
		m.StopCollector(taskID)
	}
}

// GetStats 获取统计信息
func (m *CollectorManager) GetStats() CollectorManagerStats {
	return m.stats
}

// GetHealthStatus 获取健康状态
func (m *CollectorManager) GetHealthStatus() map[string]*CollectorHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()

	result := make(map[string]*CollectorHealth)
	for k, v := range m.healthStatus {
		result[k] = v
	}
	return result
}
