# DevInsight 实现指南：GitHub Copilot 提示词

这个文档提供了使用 GitHub Copilot 实现 DevInsight 项目的提示词指南。遵循这些指南，可以帮助 Copilot 生成更准确、更匹配项目需求的代码，提高开发效率。

## 项目概述提示词

```
我正在开发一个名为 DevInsight 的设备监控与告警系统，它是一个分布式架构，包含控制平面和代理两部分。
控制平面负责管理代理、存储数据、处理告警，并提供 Web API。
代理负责连接设备并采集指标数据，通过 gRPC 与控制平面通信。
项目使用 Go 语言开发，控制平面使用 SQLite 存储数据，使用 GORM 作为 ORM。
```

## 架构设计提示词

### 控制平面结构

```
请帮我设计 DevInsight 控制平面的代码结构，包括以下模块：
1. 数据模型 (model)：定义系统实体如 Agent、Device、CollectorTask、MetricData、AlertRule、AlertEvent、User
2. 数据访问层 (repository)：定义针对每个模型的 CRUD 操作接口和实现 
3. 业务逻辑层 (service)：处理业务逻辑，如代理管理、设备管理、采集任务管理、指标处理、告警检测
4. 传输层 (transport)：包含 HTTP API 和 gRPC 服务接口
5. 配置管理：加载和管理系统配置
```

### 代理结构

```
请帮我设计 DevInsight 代理的代码结构，包括以下功能：
1. 与控制平面的 gRPC 连接管理
2. 采集器框架：支持不同类型采集器的插件化设计
3. 采集任务管理：管理来自控制平面的采集任务配置
4. 指标数据采集与上报：按设定频率采集指标并上报
5. 健康状态与心跳机制：定期向控制平面报告状态
```

## 数据模型提示词

```
请使用 GORM 定义以下 DevInsight 数据模型：
1. Agent：代理信息，包括 ID、IP、支持的采集器类型、状态、上次心跳时间
2. Device：设备信息，包括名称、类型、连接参数、状态等
3. CollectorTask：采集任务，关联设备和代理，包括采集频率、采集项等
4. MetricData：指标数据，包括设备 ID、指标名、数值、时间戳、标签等
5. AlertRule：告警规则，指定指标阈值、持续时间、通知方式等
6. AlertEvent：告警事件，记录触发的告警信息
7. User：系统用户，包括用户名、密码哈希、角色等
```

## 服务层提示词

### 代理服务

```
请实现 DevInsight 的 AgentService，负责管理代理及其与控制平面的通信。
包括以下功能：
1. 代理注册与心跳：处理代理的注册请求和心跳更新
2. 代理流管理：维护与每个代理的双向 gRPC 流
3. 任务配置下发：向代理下发采集任务配置
4. 任务状态更新：处理来自代理的任务状态更新
```

### 设备服务

```
请实现 DevInsight 的 DeviceService，负责管理监控设备。
包括以下功能：
1. 设备 CRUD：创建、读取、更新、删除设备
2. 设备状态管理：更新设备的连接状态
3. 设备与代理关联：管理设备与代理的绑定关系
```

### 任务服务

```
请实现 DevInsight 的 CollectorTaskService，负责管理采集任务。
包括以下功能：
1. 任务 CRUD：创建、读取、更新、删除采集任务
2. 任务启停控制：启用或禁用采集任务
3. 任务状态管理：更新采集任务的执行状态
4. 任务配置同步：确保任务配置同步到相应代理
```

### 指标服务

```
请实现 DevInsight 的 MetricService，负责处理指标数据。
包括以下功能：
1. 指标数据存储：保存来自代理的指标数据
2. 指标数据查询：按设备、指标类型、时间范围查询数据
3. 指标聚合与统计：计算统计值如平均值、最大值等
4. 告警检测：检查指标是否触发告警规则
```

### 告警服务

```
请实现 DevInsight 的 AlertService，负责告警规则管理和事件处理。
包括以下功能：
1. 告警规则 CRUD：管理告警规则的创建、更新、删除
2. 告警事件处理：创建、更新告警事件
3. 通知发送：根据告警发送邮件或其他通知
4. 告警状态管理：维护活跃告警与已解决告警
```

## 通信接口提示词

### gRPC 接口

```
请使用 Protocol Buffers 定义 DevInsight 控制平面与代理之间的 gRPC 接口：
1. RegisterAgent：代理注册接口
2. StreamCollectorTasks：任务配置双向流接口，下发任务配置并接收任务状态
3. StreamMetricData：指标数据流接口，接收代理上报的指标数据
```

### HTTP API 接口

```
请使用 Gin 框架实现 DevInsight 的 HTTP API 接口：
1. 认证 API：用户登录、注销
2. 代理 API：查询代理列表、状态
3. 设备 API：设备的 CRUD 操作
4. 任务 API：采集任务的 CRUD 与控制操作
5. 指标 API：查询指标数据
6. 告警 API：告警规则管理与事件查询
7. 用户 API：用户管理
```

## 采集器实现提示词

```
请实现 DevInsight 代理的采集器框架，支持以下功能：
1. 采集器接口：定义统一的采集器接口
2. 采集器管理：注册、启动、停止、配置采集器
3. 指标上报：统一的指标数据结构与上报机制
4. 采集任务调度：按配置的频率执行采集任务

并实现以下具体采集器：
1. 系统采集器：采集 CPU、内存、磁盘等系统指标
2. MySQL 采集器：采集 MySQL 性能指标
3. Redis 采集器：采集 Redis 性能指标
```

## 克服 Claude 3.7 对 Go 项目结构的理解限制

针对 Claude 3.7 在处理 Go 项目结构时可能的局限性，您可以使用以下提示词以获得更好的结果：

```
我正在使用 Go 语言开发 DevInsight 项目。请注意 Go 项目的标准结构：
1. 每个包应该位于自己的目录下
2. 包名通常与目录名一致
3. main 包应包含程序的入口点
4. 导入路径应反映项目仓库路径

请生成的代码时遵循上述 Go 项目结构，并使用 Go 模块管理依赖。
项目导入路径为 `aiops/devinsight`。
```

## 克服代码拆分问题

Claude 3.7 可能无法在单次回答中生成完整项目代码。使用以下提示词处理代码拆分：

```
请按照以下顺序生成 DevInsight 项目代码，确保每个文件的导入和依赖关系正确：
1. 首先生成 proto/devinsight.proto 文件
2. 然后生成数据模型 (model) 定义
3. 接着生成仓库接口和实现 (repository)
4. 然后是服务层实现 (service)
5. 最后是传输层接口 (HTTP/gRPC)
```

## 应对 gRPC 代码生成问题

Claude 3.7 可能难以正确实现 gRPC 代码生成步骤。使用以下提示词：

```
假设我们已经从 devinsight.proto 使用 protoc 生成了 Go 代码。请直接实现基于这些生成代码的 gRPC 服务器和客户端，使用以下导入路径：
`import "aiops/devinsight/pkg/proto"`
```

## 针对数据库和并发处理的提示词

```
请确保在实现 DevInsight 时遵循以下 Go 最佳实践：
1. 使用适当的互斥锁保护共享数据结构
2. 设计数据库事务以确保一致性
3. 妥善处理 goroutine 泄漏问题
4. 实现优雅关闭机制
5. 使用上下文 (context.Context) 处理超时和取消
```

## 测试相关提示词

```
请为 DevInsight 生成单元测试，涵盖以下方面：
1. 服务层主要功能的测试
2. 使用模拟对象替代外部依赖
3. 测试数据库操作使用内存 SQLite
4. 测试 HTTP API 端点
5. 测试 gRPC 调用
```

## 部署相关提示词

```
请为 DevInsight 创建 Docker 部署配置，包括：
1. 多阶段构建的 Dockerfile，分别构建控制平面和代理
2. Docker Compose 配置，设置控制平面和代理服务
3. 合理的环境变量和卷挂载配置
4. 健康检查和重启策略
```

## 针对特定功能的提示词

### 告警检测与通知

```
请实现 DevInsight 的告警检测逻辑，包括：
1. 针对新指标数据检查告警规则
2. 判断指标是否超过阈值
3. 检查告警持续时间
4. 避免重复告警
5. 实现不同告警级别
6. 使用邮件通知
```

### 指标数据存储与查询

```
请实现 DevInsight 的指标数据存储与查询逻辑，包括：
1. 高效存储指标数据
2. 支持按设备、指标名、时间范围查询
3. 实现数据聚合查询（如 avg, max, min）
4. 处理时序数据的特殊需求
5. 实现数据自动清理机制，保留最近 N 天数据
```

### 认证与授权

```
请实现 DevInsight 的认证与授权机制，包括：
1. 基于 JWT 的用户认证
2. 用户密码加密存储
3. 基于角色的访问控制 (RBAC)
4. API 访问权限验证
5. 安全日志记录
```

## 总结

通过上述提示词，您可以引导 GitHub Copilot 生成 DevInsight 项目的各个组成部分。根据开发进度和需求，组合或调整这些提示词，以获得最佳的代码生成效果。记住，生成的代码仍需审查和测试，以确保符合项目需求和质量标准。
