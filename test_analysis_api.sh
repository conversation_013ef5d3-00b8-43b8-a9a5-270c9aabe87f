#!/bin/bash

# 测试分析和异常检测API的完整功能
set -e

BASE_URL="http://localhost:8080"
TOKEN="dev-token"

echo "=== DevInsight 插件分析API测试 ==="
echo

# 测试函数
test_api() {
    local endpoint="$1"
    local data="$2"
    local description="$3"
    
    echo "测试: $description"
    echo "调用: $endpoint"
    echo "数据: $data"
    echo
    
    response=$(curl -s -X POST "${BASE_URL}${endpoint}?token=${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "$data")
    
    echo "响应:"
    echo "$response" | jq
    echo
    echo "---"
    echo
}

# 1. 测试指标分析
echo "1. 测试指标分析API"
test_api "/api/plugins/analyze" '{
    "device_id": "server-001",
    "start_time": 1732545000,
    "end_time": 1732548600,
    "limit": 50
}' "分析50个指标数据点"

# 2. 测试异常检测 - 低阈值
echo "2. 测试异常检测API (低阈值)"
test_api "/api/plugins/detect-anomalies" '{
    "device_id": "server-002",
    "start_time": 1732545000,
    "end_time": 1732548600,
    "threshold": 1.0
}' "异常检测 - 阈值1.0"

# 3. 测试异常检测 - 更高阈值
echo "3. 测试异常检测API (高阈值)"
test_api "/api/plugins/detect-anomalies" '{
    "device_id": "server-003",
    "start_time": 1732545000,
    "end_time": 1732548600,
    "threshold": 3.0
}' "异常检测 - 阈值3.0"

# 4. 连续多次调用同一设备进行分析 - 积累数据
echo "4. 连续多次分析同一设备以积累数据"
for i in {1..5}; do
    echo "第 $i 次分析同一设备..."
    test_api "/api/plugins/analyze" "{
        \"device_id\": \"persistent-device\",
        \"start_time\": $((1732545000 + i * 3600)),
        \"end_time\": $((1732548600 + i * 3600)),
        \"limit\": 30
    }" "持续设备分析 - 第${i}次"
    
    # 短暂等待
    sleep 1
done

# 5. 对积累了数据的设备进行异常检测
echo "5. 对积累数据的设备进行异常检测"
test_api "/api/plugins/detect-anomalies" '{
    "device_id": "persistent-device",
    "start_time": 1732570000,
    "end_time": 1732573600,
    "threshold": 1.5
}' "持续设备的异常检测"

echo "=== 测试完成 ==="
