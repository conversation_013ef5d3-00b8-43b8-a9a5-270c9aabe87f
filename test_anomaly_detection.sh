#!/bin/bash

# 全面测试异常检测功能
set -e

BASE_URL="http://localhost:8080"
TOKEN="dev-token"

echo "=== DevInsight 异常检测深度测试 ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试函数
test_api() {
    local endpoint="$1"
    local data="$2"
    local description="$3"
    local expected_result="$4"
    
    log_info "测试: $description"
    echo "调用: $endpoint"
    
    response=$(curl -s -X POST "${BASE_URL}${endpoint}?token=${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "$data")
    
    echo "响应:"
    echo "$response" | jq
    
    # 检查响应是否包含期望的结果
    if [[ -n "$expected_result" ]]; then
        if echo "$response" | jq -e ".data | $expected_result" > /dev/null 2>&1; then
            log_success "测试通过: 找到期望的结果"
        else
            log_warning "测试警告: 未找到期望的结果 ($expected_result)"
        fi
    fi
    
    echo
    echo "---"
    echo
}

# 1. 首先检查插件状态
log_info "1. 检查插件系统状态"
echo "获取插件列表..."
response=$(curl -s "${BASE_URL}/api/plugins?token=${TOKEN}")
echo "$response" | jq

analyzer_count=$(echo "$response" | jq '.data.plugins | [.[] | select(.type == "analyzer")] | length')
log_info "找到 $analyzer_count 个分析器插件"

if [[ "$analyzer_count" -eq 0 ]]; then
    log_error "没有找到分析器插件，无法进行异常检测测试"
    exit 1
fi

echo
echo "---"
echo

# 2. 测试基本分析功能
log_info "2. 测试基本指标分析功能"
test_api "/api/plugins/analyze" '{
    "device_id": "test-server-001",
    "start_time": 1732545000,
    "end_time": 1732548600,
    "limit": 20
}' "基本分析测试" '.analysis_results | length > 0'

# 3. 累积大量数据进行深度测试
log_info "3. 累积数据进行深度异常检测测试"

DEVICE_ID="anomaly-test-device"
BASE_TIME=1732545000

# 为设备累积30次数据
for i in {1..30}; do
    echo "累积第 $i 批数据..."
    
    # 每批数据包含不同时间点，模拟真实场景
    START_TIME=$((BASE_TIME + i * 300))  # 每5分钟一批
    END_TIME=$((START_TIME + 300))
    
    curl -s -X POST "${BASE_URL}/api/plugins/analyze?token=${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"device_id\": \"${DEVICE_ID}\",
            \"start_time\": ${START_TIME},
            \"end_time\": ${END_TIME},
            \"limit\": 15
        }" > /dev/null
    
    # 每10次输出一次进度
    if [[ $((i % 10)) -eq 0 ]]; then
        log_info "已累积 $i 批数据"
    fi
done

log_success "数据累积完成，共30批数据"
echo

# 4. 现在进行异常检测测试
log_info "4. 开始异常检测测试"

# 测试不同的阈值
thresholds=(1.0 1.5 2.0 2.5 3.0)

for threshold in "${thresholds[@]}"; do
    log_info "测试阈值: $threshold"
    
    test_api "/api/plugins/detect-anomalies" "{
        \"device_id\": \"${DEVICE_ID}\",
        \"start_time\": $((BASE_TIME + 3000)),
        \"end_time\": $((BASE_TIME + 6000)),
        \"threshold\": ${threshold}
    }" "异常检测 - 阈值${threshold}" 'has("anomaly_count")'
    
done

# 5. 测试不同设备的异常检测
log_info "5. 测试多设备异常检测"

devices=("server-prod-001" "server-prod-002" "server-test-001")

for device in "${devices[@]}"; do
    log_info "测试设备: $device"
    
    # 先为每个设备累积一些数据
    for j in {1..5}; do
        START_TIME=$((BASE_TIME + j * 600))
        END_TIME=$((START_TIME + 300))
        
        curl -s -X POST "${BASE_URL}/api/plugins/analyze?token=${TOKEN}" \
            -H "Content-Type: application/json" \
            -d "{
                \"device_id\": \"${device}\",
                \"start_time\": ${START_TIME},
                \"end_time\": ${END_TIME},
                \"limit\": 10
            }" > /dev/null
    done
    
    # 进行异常检测
    test_api "/api/plugins/detect-anomalies" "{
        \"device_id\": \"${device}\",
        \"start_time\": $((BASE_TIME + 3600)),
        \"end_time\": $((BASE_TIME + 7200)),
        \"threshold\": 2.0
    }" "设备${device}异常检测" 'has("anomaly_count")'
    
done

# 6. 测试边界情况
log_info "6. 测试边界情况"

# 测试空数据
test_api "/api/plugins/detect-anomalies" '{
    "device_id": "non-existent-device",
    "start_time": 1732545000,
    "end_time": 1732548600,
    "threshold": 2.0
}' "不存在设备的异常检测" '.anomaly_count == 0'

# 测试极低阈值
test_api "/api/plugins/detect-anomalies" "{
    \"device_id\": \"${DEVICE_ID}\",
    \"start_time\": $((BASE_TIME + 1000)),
    \"end_time\": $((BASE_TIME + 4000)),
    \"threshold\": 0.1
}" "极低阈值异常检测" 'has("anomaly_count")'

# 测试极高阈值
test_api "/api/plugins/detect-anomalies" "{
    \"device_id\": \"${DEVICE_ID}\",
    \"start_time\": $((BASE_TIME + 1000)),
    \"end_time\": $((BASE_TIME + 4000)),
    \"threshold\": 10.0
}" "极高阈值异常检测" '.anomaly_count >= 0'

# 7. 性能测试
log_info "7. 性能测试"

log_info "测试大批量数据处理..."
start_time=$(date +%s)

test_api "/api/plugins/analyze" "{
    \"device_id\": \"${DEVICE_ID}\",
    \"start_time\": ${BASE_TIME},
    \"end_time\": $((BASE_TIME + 9000)),
    \"limit\": 100
}" "大批量数据分析" '.analysis_results | length > 0'

end_time=$(date +%s)
duration=$((end_time - start_time))
log_info "大批量处理用时: ${duration}秒"

# 8. 检查插件健康状态
log_info "8. 检查插件健康状态"
health_response=$(curl -s -X POST "${BASE_URL}/api/plugins/health-check?token=${TOKEN}")
echo "插件健康状态:"
echo "$health_response" | jq

# 统计结果
echo
echo "===================="
log_success "异常检测深度测试完成"
echo "===================="
echo
log_info "测试总结:"
echo "- 累积了充足的历史数据用于异常检测"
echo "- 测试了多种阈值设置"
echo "- 验证了多设备场景"
echo "- 检查了边界情况和性能"
echo "- 验证了插件健康状态"
echo
log_info "如果异常检测仍然没有发现异常，可能的原因："
echo "- 生成的测试数据方差不够大"
echo "- 需要调整simple-analyzer插件的检测算法"
echo "- 需要更多的历史数据点"
