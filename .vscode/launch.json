{"version": "0.2.0", "configurations": [{"name": "control_plane", "type": "go", "request": "launch", "mode": "auto", "cwd": "${workspaceFolder}", "program": "${workspaceFolder}/control_plane/cmd", "env": {}, "buildFlags": ["-tags=sonic"], "showLog": false}, {"name": "agent", "type": "go", "request": "launch", "mode": "auto", "cwd": "${workspaceFolder}", "program": "${workspaceFolder}/agent/cmd", "env": {}, "buildFlags": ["-tags=sonic"], "showLog": false}, {"name": "migration", "type": "go", "request": "launch", "mode": "auto", "cwd": "${workspaceFolder}", "program": "${workspaceFolder}/cmd/gen", "env": {}, "buildFlags": ["-tags=sonic"], "showLog": false}]}