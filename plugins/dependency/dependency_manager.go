package dependency

import (
	"fmt"
	"sort"
	"sync"

	plugininterface "aiops/plugins/interface"

	"go.uber.org/zap"
)

// DependencyResolver 依赖解析器
type DependencyResolver struct {
	mu           sync.RWMutex
	plugins      map[string]*plugininterface.EnhancedPluginInfo
	capabilities map[string][]string // capability -> []pluginNames
	logger       *zap.Logger
}

// NewDependencyResolver 创建新的依赖解析器
func NewDependencyResolver(logger *zap.Logger) *DependencyResolver {
	return &DependencyResolver{
		plugins:      make(map[string]*plugininterface.EnhancedPluginInfo),
		capabilities: make(map[string][]string),
		logger:       logger,
	}
}

// RegisterPlugin 注册插件信息
func (r *DependencyResolver) RegisterPlugin(info *plugininterface.EnhancedPluginInfo) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", info.Name, info.Version)

	// 检查是否已注册
	if _, exists := r.plugins[pluginKey]; exists {
		return fmt.Errorf("plugin %s already registered", pluginKey)
	}

	// 注册插件
	r.plugins[pluginKey] = info

	// 注册能力
	for _, capability := range info.Capabilities {
		capabilityKey := fmt.Sprintf("%s:%s", capability.Name, capability.Version)
		r.capabilities[capabilityKey] = append(r.capabilities[capabilityKey], pluginKey)
	}

	r.logger.Info("Plugin registered for dependency resolution",
		zap.String("plugin", pluginKey),
		zap.Int("capabilities", len(info.Capabilities)))

	return nil
}

// UnregisterPlugin 注销插件
func (r *DependencyResolver) UnregisterPlugin(name, version string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)

	info, exists := r.plugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin %s not registered", pluginKey)
	}

	// 移除能力注册
	for _, capability := range info.Capabilities {
		capabilityKey := fmt.Sprintf("%s:%s", capability.Name, capability.Version)
		providers := r.capabilities[capabilityKey]
		for i, provider := range providers {
			if provider == pluginKey {
				r.capabilities[capabilityKey] = append(providers[:i], providers[i+1:]...)
				break
			}
		}
		// 如果没有提供者了，删除能力键
		if len(r.capabilities[capabilityKey]) == 0 {
			delete(r.capabilities, capabilityKey)
		}
	}

	// 删除插件
	delete(r.plugins, pluginKey)

	r.logger.Info("Plugin unregistered from dependency resolution",
		zap.String("plugin", pluginKey))

	return nil
}

// ResolveDependencies 解析插件依赖
func (r *DependencyResolver) ResolveDependencies(pluginName, version string) (*DependencyGraph, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	pluginKey := fmt.Sprintf("%s:%s", pluginName, version)

	// 构建依赖图
	graph := NewDependencyGraph()
	visited := make(map[string]bool)

	if err := r.buildDependencyGraph(pluginKey, graph, visited); err != nil {
		return nil, err
	}

	// 检查循环依赖
	if cycles := graph.DetectCycles(); len(cycles) > 0 {
		return nil, fmt.Errorf("circular dependencies detected: %v", cycles)
	}

	return graph, nil
}

// GetLoadOrder 获取加载顺序
func (r *DependencyResolver) GetLoadOrder(pluginName, version string) ([]string, error) {
	graph, err := r.ResolveDependencies(pluginName, version)
	if err != nil {
		return nil, err
	}

	return graph.TopologicalSort(), nil
}

// ValidateDependencies 验证依赖关系
func (r *DependencyResolver) ValidateDependencies(info *plugininterface.EnhancedPluginInfo) error {
	r.mu.RLock()
	defer r.mu.RUnlock()

	for _, dep := range info.Dependencies {
		// 检查依赖是否存在
		if !r.hasDependency(&dep) {
			if dep.Required {
				return fmt.Errorf("required dependency %s not found", dep.Name)
			}
			r.logger.Warn("Optional dependency not found",
				zap.String("dependency", dep.Name))
			continue
		}

		// 检查版本兼容性
		if !r.isVersionCompatible(&dep) {
			return fmt.Errorf("dependency %s version incompatible", dep.Name)
		}

		// 检查冲突
		for _, conflict := range dep.ConflictsWith {
			if r.hasPlugin(conflict) {
				return fmt.Errorf("conflicting plugin %s is present", conflict)
			}
		}
	}

	return nil
}

// CheckConflicts 检查插件冲突
func (r *DependencyResolver) CheckConflicts(info *plugininterface.EnhancedPluginInfo) []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var conflicts []string

	for _, dep := range info.Dependencies {
		for _, conflict := range dep.ConflictsWith {
			if r.hasPlugin(conflict) {
				conflicts = append(conflicts, conflict)
			}
		}
	}

	return conflicts
}

// buildDependencyGraph 构建依赖图
func (r *DependencyResolver) buildDependencyGraph(pluginKey string, graph *DependencyGraph, visited map[string]bool) error {
	if visited[pluginKey] {
		return nil
	}
	visited[pluginKey] = true

	info, exists := r.plugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin %s not found", pluginKey)
	}

	// 添加节点
	graph.AddNode(pluginKey, info)

	// 处理依赖
	for _, dep := range info.Dependencies {
		depPlugin := r.findBestMatch(&dep)
		if depPlugin == "" {
			if dep.Required {
				return fmt.Errorf("required dependency %s not found", dep.Name)
			}
			continue
		}

		// 添加边
		graph.AddEdge(depPlugin, pluginKey)

		// 递归构建依赖图
		if err := r.buildDependencyGraph(depPlugin, graph, visited); err != nil {
			return err
		}
	}

	return nil
}

// findBestMatch 找到最佳匹配的依赖
func (r *DependencyResolver) findBestMatch(dep *plugininterface.PluginDependency) string {
	var candidates []string

	// 查找所有匹配的插件
	for pluginKey, info := range r.plugins {
		if info.Name == dep.Name {
			if r.isVersionInRange(info.Version, dep.MinVersion, dep.MaxVersion) {
				candidates = append(candidates, pluginKey)
			}
		}
	}

	if len(candidates) == 0 {
		return ""
	}

	// 选择最新版本
	sort.Slice(candidates, func(i, j int) bool {
		versionI := r.plugins[candidates[i]].Version
		versionJ := r.plugins[candidates[j]].Version
		return compareVersions(versionI, versionJ) > 0
	})

	return candidates[0]
}

// hasDependency 检查是否有依赖
func (r *DependencyResolver) hasDependency(dep *plugininterface.PluginDependency) bool {
	for _, info := range r.plugins {
		if info.Name == dep.Name {
			if r.isVersionInRange(info.Version, dep.MinVersion, dep.MaxVersion) {
				return true
			}
		}
	}
	return false
}

// hasPlugin 检查是否有插件
func (r *DependencyResolver) hasPlugin(name string) bool {
	for _, info := range r.plugins {
		if info.Name == name {
			return true
		}
	}
	return false
}

// isVersionCompatible 检查版本兼容性
func (r *DependencyResolver) isVersionCompatible(dep *plugininterface.PluginDependency) bool {
	for _, info := range r.plugins {
		if info.Name == dep.Name {
			return r.isVersionInRange(info.Version, dep.MinVersion, dep.MaxVersion)
		}
	}
	return false
}

// isVersionInRange 检查版本是否在范围内
func (r *DependencyResolver) isVersionInRange(version, minVersion, maxVersion string) bool {
	if minVersion != "" && compareVersions(version, minVersion) < 0 {
		return false
	}
	if maxVersion != "" && compareVersions(version, maxVersion) > 0 {
		return false
	}
	return true
}

// DependencyGraph 依赖图
type DependencyGraph struct {
	nodes map[string]*plugininterface.EnhancedPluginInfo
	edges map[string][]string // from -> []to
}

// NewDependencyGraph 创建新的依赖图
func NewDependencyGraph() *DependencyGraph {
	return &DependencyGraph{
		nodes: make(map[string]*plugininterface.EnhancedPluginInfo),
		edges: make(map[string][]string),
	}
}

// AddNode 添加节点
func (g *DependencyGraph) AddNode(key string, info *plugininterface.EnhancedPluginInfo) {
	g.nodes[key] = info
}

// AddEdge 添加边 (from -> to)
func (g *DependencyGraph) AddEdge(from, to string) {
	g.edges[from] = append(g.edges[from], to)
}

// DetectCycles 检测循环依赖
func (g *DependencyGraph) DetectCycles() [][]string {
	var cycles [][]string
	visited := make(map[string]int) // 0: unvisited, 1: visiting, 2: visited
	var path []string

	for node := range g.nodes {
		if visited[node] == 0 {
			if cycle := g.dfsDetectCycle(node, visited, path); cycle != nil {
				cycles = append(cycles, cycle)
			}
		}
	}

	return cycles
}

// dfsDetectCycle DFS检测循环
func (g *DependencyGraph) dfsDetectCycle(node string, visited map[string]int, path []string) []string {
	visited[node] = 1 // visiting
	path = append(path, node)

	for _, neighbor := range g.edges[node] {
		if visited[neighbor] == 1 {
			// 找到循环
			cycleStart := -1
			for i, p := range path {
				if p == neighbor {
					cycleStart = i
					break
				}
			}
			if cycleStart >= 0 {
				return append(path[cycleStart:], neighbor)
			}
		} else if visited[neighbor] == 0 {
			if cycle := g.dfsDetectCycle(neighbor, visited, path); cycle != nil {
				return cycle
			}
		}
	}

	visited[node] = 2 // visited
	return nil
}

// TopologicalSort 拓扑排序
func (g *DependencyGraph) TopologicalSort() []string {
	visited := make(map[string]bool)
	var stack []string

	// DFS
	var dfs func(string)
	dfs = func(node string) {
		visited[node] = true
		for _, neighbor := range g.edges[node] {
			if !visited[neighbor] {
				dfs(neighbor)
			}
		}
		stack = append([]string{node}, stack...) // 前插
	}

	for node := range g.nodes {
		if !visited[node] {
			dfs(node)
		}
	}

	return stack
}

// 版本比较工具函数
func compareVersions(v1, v2 string) int {
	// 简单的版本比较实现
	// 在实际应用中应该使用更完善的语义版本比较
	if v1 == v2 {
		return 0
	}
	if v1 > v2 {
		return 1
	}
	return -1
}

// DependencyManager 依赖管理器
type DependencyManager struct {
	resolver *DependencyResolver
	logger   *zap.Logger
}

// NewDependencyManager 创建依赖管理器
func NewDependencyManager(logger *zap.Logger) *DependencyManager {
	return &DependencyManager{
		resolver: NewDependencyResolver(logger),
		logger:   logger,
	}
}

// ValidateAndResolve 验证并解析依赖
func (m *DependencyManager) ValidateAndResolve(info *plugininterface.EnhancedPluginInfo) (*DependencyResolution, error) {
	// 验证依赖
	if err := m.resolver.ValidateDependencies(info); err != nil {
		return nil, fmt.Errorf("dependency validation failed: %w", err)
	}

	// 检查冲突
	conflicts := m.resolver.CheckConflicts(info)
	if len(conflicts) > 0 {
		return nil, fmt.Errorf("dependency conflicts detected: %v", conflicts)
	}

	// 解析依赖
	graph, err := m.resolver.ResolveDependencies(info.Name, info.Version)
	if err != nil {
		return nil, fmt.Errorf("dependency resolution failed: %w", err)
	}

	// 获取加载顺序
	loadOrder := graph.TopologicalSort()

	return &DependencyResolution{
		Plugin:    info,
		Graph:     graph,
		LoadOrder: loadOrder,
		Conflicts: conflicts,
	}, nil
}

// DependencyResolution 依赖解析结果
type DependencyResolution struct {
	Plugin    *plugininterface.EnhancedPluginInfo `json:"plugin"`
	Graph     *DependencyGraph                    `json:"graph"`
	LoadOrder []string                            `json:"load_order"`
	Conflicts []string                            `json:"conflicts"`
}
