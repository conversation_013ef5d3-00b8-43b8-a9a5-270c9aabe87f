package security

import (
	"context"
	"fmt"
	"os"
	"runtime"
	"sync"
	"syscall"
	"time"

	plugininterface "aiops/plugins/interface"

	"go.uber.org/zap"
)

// SecurityManager 插件安全管理器（导出类型）
type SecurityManager = PluginSecurityManager

// PluginSecurityManager 插件安全管理器
type PluginSecurityManager struct {
	mu              sync.RWMutex
	permissions     map[string]*plugininterface.PluginPermission
	resourceLimits  map[string]*plugininterface.PluginResourceLimits
	resourceMonitor *ResourceMonitor
	logger          *zap.Logger
}

// NewPluginSecurityManager 创建新的插件安全管理器
func NewPluginSecurityManager(logger *zap.Logger) *PluginSecurityManager {
	return &PluginSecurityManager{
		permissions:     make(map[string]*plugininterface.PluginPermission),
		resourceLimits:  make(map[string]*plugininterface.PluginResourceLimits),
		resourceMonitor: NewResourceMonitor(),
		logger:          logger,
	}
}

// SetPluginPermissions 设置插件权限
func (m *PluginSecurityManager) SetPluginPermissions(pluginKey string, permissions *plugininterface.PluginPermission) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 验证权限配置
	if err := m.validatePermissions(permissions); err != nil {
		return fmt.Errorf("invalid permissions: %w", err)
	}

	m.permissions[pluginKey] = permissions
	m.logger.Info("Plugin permissions set", zap.String("plugin", pluginKey))
	return nil
}

// GetPluginPermissions 获取插件权限
func (m *PluginSecurityManager) GetPluginPermissions(pluginKey string) (*plugininterface.PluginPermission, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	permissions, exists := m.permissions[pluginKey]
	if !exists {
		return nil, fmt.Errorf("permissions not found for plugin %s", pluginKey)
	}

	return permissions, nil
}

// CheckNetworkAccess 检查网络访问权限
func (m *PluginSecurityManager) CheckNetworkAccess(pluginKey, host string) error {
	permissions, err := m.GetPluginPermissions(pluginKey)
	if err != nil {
		return err
	}

	if !permissions.CanAccessNetwork {
		return fmt.Errorf("plugin %s is not allowed to access network", pluginKey)
	}

	// 检查允许的主机列表
	if len(permissions.AllowedNetworkHosts) > 0 {
		allowed := false
		for _, allowedHost := range permissions.AllowedNetworkHosts {
			if host == allowedHost || matchPattern(host, allowedHost) {
				allowed = true
				break
			}
		}
		if !allowed {
			return fmt.Errorf("plugin %s is not allowed to access host %s", pluginKey, host)
		}
	}

	return nil
}

// CheckFileSystemAccess 检查文件系统访问权限
func (m *PluginSecurityManager) CheckFileSystemAccess(pluginKey, path string) error {
	permissions, err := m.GetPluginPermissions(pluginKey)
	if err != nil {
		return err
	}

	if !permissions.CanAccessFileSystem {
		return fmt.Errorf("plugin %s is not allowed to access file system", pluginKey)
	}

	// 检查允许的路径列表
	if len(permissions.AllowedPaths) > 0 {
		allowed := false
		for _, allowedPath := range permissions.AllowedPaths {
			if isPathAllowed(path, allowedPath) {
				allowed = true
				break
			}
		}
		if !allowed {
			return fmt.Errorf("plugin %s is not allowed to access path %s", pluginKey, path)
		}
	}

	return nil
}

// CheckCommandExecution 检查命令执行权限
func (m *PluginSecurityManager) CheckCommandExecution(pluginKey, command string) error {
	permissions, err := m.GetPluginPermissions(pluginKey)
	if err != nil {
		return err
	}

	if !permissions.CanExecuteCommands {
		return fmt.Errorf("plugin %s is not allowed to execute commands", pluginKey)
	}

	// 检查允许的命令列表
	if len(permissions.AllowedCommands) > 0 {
		allowed := false
		for _, allowedCmd := range permissions.AllowedCommands {
			if command == allowedCmd || matchPattern(command, allowedCmd) {
				allowed = true
				break
			}
		}
		if !allowed {
			return fmt.Errorf("plugin %s is not allowed to execute command %s", pluginKey, command)
		}
	}

	return nil
}

// StartResourceMonitoring 开始资源监控
func (m *PluginSecurityManager) StartResourceMonitoring(pluginKey string, pid int) error {
	permissions, err := m.GetPluginPermissions(pluginKey)
	if err != nil {
		return err
	}

	if permissions.ResourceLimits == nil {
		return nil // 没有资源限制
	}

	return m.resourceMonitor.StartMonitoring(pluginKey, pid, permissions.ResourceLimits)
}

// StopResourceMonitoring 停止资源监控
func (m *PluginSecurityManager) StopResourceMonitoring(pluginKey string) {
	m.resourceMonitor.StopMonitoring(pluginKey)
}

// GetResourceUsage 获取资源使用情况
func (m *PluginSecurityManager) GetResourceUsage(pluginKey string) (*ResourceUsage, error) {
	return m.resourceMonitor.GetUsage(pluginKey)
}

// validatePermissions 验证权限配置
func (m *PluginSecurityManager) validatePermissions(permissions *plugininterface.PluginPermission) error {
	if permissions == nil {
		return fmt.Errorf("permissions cannot be nil")
	}

	// 验证资源限制
	if permissions.ResourceLimits != nil {
		limits := permissions.ResourceLimits
		if limits.MaxCPUPercent < 0 || limits.MaxCPUPercent > 100 {
			return fmt.Errorf("invalid CPU limit: %f", limits.MaxCPUPercent)
		}
		if limits.MaxMemoryMB < 0 {
			return fmt.Errorf("invalid memory limit: %d", limits.MaxMemoryMB)
		}
		if limits.MaxGoroutines < 0 {
			return fmt.Errorf("invalid goroutine limit: %d", limits.MaxGoroutines)
		}
	}

	return nil
}

// ResourceMonitor 资源监控器
type ResourceMonitor struct {
	mu       sync.RWMutex
	monitors map[string]*PluginResourceMonitor
	logger   *zap.Logger
}

// logResourceUsage 记录资源使用情况
func (rm *ResourceMonitor) logResourceUsage(pluginKey string, usage *plugininterface.ResourceUsage) {
	rm.logger.Info("Plugin resource usage",
		zap.String("plugin", pluginKey),
		zap.Float64("cpu_percent", usage.CPUPercent),
		zap.Int64("memory_mb", usage.MemoryMB),
		zap.Int64("disk_mb", usage.DiskMB),
		zap.Int("goroutines", usage.GoroutineCount),
	)
}

// PluginResourceMonitor 插件资源监控器
type PluginResourceMonitor struct {
	pluginKey string
	pid       int
	limits    *plugininterface.PluginResourceLimits
	usage     *ResourceUsage
	stopCh    chan struct{}
	ticker    *time.Ticker
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	CPUPercent       float64   `json:"cpu_percent"`
	MemoryMB         int64     `json:"memory_mb"`
	DiskMB           int64     `json:"disk_mb"`
	GoroutineCount   int       `json:"goroutine_count"`
	OpenFileCount    int       `json:"open_file_count"`
	NetworkConnCount int       `json:"network_conn_count"`
	UptimeSeconds    int64     `json:"uptime_seconds"`
	LastUpdate       time.Time `json:"last_update"`
}

// NewResourceMonitor 创建新的资源监控器
func NewResourceMonitor() *ResourceMonitor {
	return &ResourceMonitor{
		monitors: make(map[string]*PluginResourceMonitor),
	}
}

// StartMonitoring 开始监控插件资源
func (m *ResourceMonitor) StartMonitoring(pluginKey string, pid int, limits *plugininterface.PluginResourceLimits) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 如果已经在监控，先停止
	if existing, exists := m.monitors[pluginKey]; exists {
		close(existing.stopCh)
	}

	monitor := &PluginResourceMonitor{
		pluginKey: pluginKey,
		pid:       pid,
		limits:    limits,
		usage:     &ResourceUsage{},
		stopCh:    make(chan struct{}),
		ticker:    time.NewTicker(5 * time.Second), // 每5秒监控一次
	}

	m.monitors[pluginKey] = monitor

	// 启动监控协程
	go monitor.monitor()

	return nil
}

// StopMonitoring 停止监控
func (m *ResourceMonitor) StopMonitoring(pluginKey string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if monitor, exists := m.monitors[pluginKey]; exists {
		close(monitor.stopCh)
		monitor.ticker.Stop()
		delete(m.monitors, pluginKey)
	}
}

// GetUsage 获取资源使用情况
func (m *ResourceMonitor) GetUsage(pluginKey string) (*ResourceUsage, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	monitor, exists := m.monitors[pluginKey]
	if !exists {
		return nil, fmt.Errorf("no monitoring data for plugin %s", pluginKey)
	}

	// 转换为 plugininterface.ResourceUsage 并记录日志
	usage := &plugininterface.ResourceUsage{
		CPUPercent:     monitor.usage.CPUPercent,
		MemoryMB:       monitor.usage.MemoryMB,
		DiskMB:         monitor.usage.DiskMB,
		GoroutineCount: monitor.usage.GoroutineCount,
		OpenFiles:      monitor.usage.OpenFileCount,
		NetworkConns:   monitor.usage.NetworkConnCount,
		LastUpdated:    monitor.usage.LastUpdate,
	}

	// 记录资源使用情况
	m.logResourceUsage(pluginKey, usage)

	return monitor.usage, nil
}

// monitor 监控协程
func (m *PluginResourceMonitor) monitor() {
	startTime := time.Now()

	for {
		select {
		case <-m.stopCh:
			return
		case <-m.ticker.C:
			// 更新资源使用情况
			m.updateUsage(startTime)

			// 检查是否超出限制
			if err := m.checkLimits(); err != nil {
				// 处理资源超限（可以发送告警、终止进程等）
				fmt.Printf("Plugin %s resource limit exceeded: %v\n", m.pluginKey, err)
			}
		}
	}
}

// updateUsage 更新资源使用情况
func (m *PluginResourceMonitor) updateUsage(startTime time.Time) {
	// 获取进程信息
	process, err := os.FindProcess(m.pid)
	if err != nil {
		return
	}

	// 更新基础信息
	m.usage.UptimeSeconds = int64(time.Since(startTime).Seconds())
	m.usage.LastUpdate = time.Now()
	m.usage.GoroutineCount = runtime.NumGoroutine()

	// 在真实环境中，这里需要使用系统调用或第三方库来获取详细的进程资源信息
	// 这里只是示例实现
	m.updateProcessStats(process)
}

// updateProcessStats 更新进程统计信息
func (m *PluginResourceMonitor) updateProcessStats(process *os.Process) {
	// 这里需要根据操作系统实现具体的资源获取逻辑
	// macOS/Linux 可以通过 /proc 文件系统或系统调用获取
	// Windows 可以通过 WMI 或 PDH API 获取

	// 示例实现（需要根据实际系统完善）
	var rusage syscall.Rusage
	if err := syscall.Getrusage(syscall.RUSAGE_SELF, &rusage); err == nil {
		// 内存使用 (KB -> MB)
		m.usage.MemoryMB = rusage.Maxrss / 1024
	}
}

// checkLimits 检查资源限制
func (m *PluginResourceMonitor) checkLimits() error {
	if m.limits == nil {
		return nil
	}

	// 检查 CPU 使用率
	if m.limits.MaxCPUPercent > 0 && m.usage.CPUPercent > m.limits.MaxCPUPercent {
		return fmt.Errorf("CPU usage %.2f%% exceeds limit %.2f%%",
			m.usage.CPUPercent, m.limits.MaxCPUPercent)
	}

	// 检查内存使用
	if m.limits.MaxMemoryMB > 0 && m.usage.MemoryMB > m.limits.MaxMemoryMB {
		return fmt.Errorf("memory usage %dMB exceeds limit %dMB",
			m.usage.MemoryMB, m.limits.MaxMemoryMB)
	}

	// 检查协程数量
	if m.limits.MaxGoroutines > 0 && m.usage.GoroutineCount > m.limits.MaxGoroutines {
		return fmt.Errorf("goroutine count %d exceeds limit %d",
			m.usage.GoroutineCount, m.limits.MaxGoroutines)
	}

	// 检查打开文件数
	if m.limits.MaxOpenFiles > 0 && m.usage.OpenFileCount > m.limits.MaxOpenFiles {
		return fmt.Errorf("open file count %d exceeds limit %d",
			m.usage.OpenFileCount, m.limits.MaxOpenFiles)
	}

	return nil
}

// 辅助函数

// matchPattern 模式匹配（简单的通配符支持）
func matchPattern(text, pattern string) bool {
	// 简单实现，支持 * 通配符
	// 在实际应用中可以使用更复杂的模式匹配
	if pattern == "*" {
		return true
	}

	// 这里可以实现更复杂的模式匹配逻辑
	return text == pattern
}

// isPathAllowed 检查路径是否被允许
func isPathAllowed(path, allowedPath string) bool {
	// 检查路径是否在允许的路径下
	// 需要处理相对路径、符号链接等情况

	// 简单实现
	if allowedPath == "/" {
		return true // 允许所有路径
	}

	// 检查是否是子路径
	return len(path) >= len(allowedPath) && path[:len(allowedPath)] == allowedPath
}

// PluginSandbox 插件沙箱
type PluginSandbox struct {
	securityManager *PluginSecurityManager
	pluginKey       string
	permissions     *plugininterface.PluginPermission
	logger          *zap.Logger
}

// NewPluginSandbox 创建插件沙箱
func NewPluginSandbox(securityManager *PluginSecurityManager, pluginKey string, logger *zap.Logger) *PluginSandbox {
	return &PluginSandbox{
		securityManager: securityManager,
		pluginKey:       pluginKey,
		logger:          logger,
	}
}

// Initialize 初始化沙箱
func (s *PluginSandbox) Initialize(permissions *plugininterface.PluginPermission) error {
	s.permissions = permissions
	return s.securityManager.SetPluginPermissions(s.pluginKey, permissions)
}

// Execute 在沙箱中执行函数
func (s *PluginSandbox) Execute(ctx context.Context, fn func() error) error {
	// 在执行前检查权限和资源限制
	if s.permissions.ResourceLimits != nil && s.permissions.ResourceLimits.ExecutionTimeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, s.permissions.ResourceLimits.ExecutionTimeout)
		defer cancel()
	}

	// 创建错误通道
	errCh := make(chan error, 1)

	// 在协程中执行函数
	go func() {
		defer func() {
			if r := recover(); r != nil {
				errCh <- fmt.Errorf("plugin panic: %v", r)
			}
		}()
		errCh <- fn()
	}()

	// 等待执行完成或超时
	select {
	case err := <-errCh:
		return err
	case <-ctx.Done():
		return fmt.Errorf("plugin execution timeout")
	}
}
