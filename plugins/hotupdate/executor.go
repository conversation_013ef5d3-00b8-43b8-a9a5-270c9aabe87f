// 插件更新执行器
package hotupdate

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/sirupsen/logrus"
)

// ==================== 更新执行器 ====================

// executeUpdatePlan 执行更新计划
func (m *HotUpdateManager) executeUpdatePlan(ctx context.Context, plan *UpdatePlan) error {
	m.logger.WithField("plan_id", plan.ID).Info("Starting update execution")

	// 设置开始状态
	plan.Status = StatusInProgress
	plan.UpdatedAt = time.Now()

	// 通知事件处理器
	for _, handler := range m.eventHandlers {
		if err := handler.OnUpdateStarted(ctx, plan); err != nil {
			m.logger.WithError(err).Warn("Event handler OnUpdateStarted failed")
		}
	}

	// 执行各个阶段
	for i, phase := range plan.Phases {
		if !phase.Required && plan.Request.SkipOptional {
			m.logger.WithField("phase", phase.Phase).Info("Skipping optional phase")
			continue
		}

		// 更新当前阶段
		plan.CurrentPhase = phase.Phase
		plan.Phases[i].Status = StatusInProgress
		plan.Phases[i].StartTime = &[]time.Time{time.Now()}[0]

		// 计算进度
		plan.Progress = float64(i) / float64(len(plan.Phases)) * 100

		// 通知阶段变更
		for _, handler := range m.eventHandlers {
			if err := handler.OnPhaseChanged(ctx, plan, phase.Phase); err != nil {
				m.logger.WithError(err).Warn("Event handler OnPhaseChanged failed")
			}
		}

		// 执行阶段
		if err := m.executePhase(ctx, plan, &phase); err != nil {
			plan.Phases[i].Status = StatusFailed
			plan.Phases[i].Error = err.Error()
			plan.Phases[i].EndTime = &[]time.Time{time.Now()}[0]

			return m.handlePhaseFailure(ctx, plan, phase.Phase, err)
		}

		// 阶段成功完成
		plan.Phases[i].Status = StatusCompleted
		plan.Phases[i].EndTime = &[]time.Time{time.Now()}[0]

		m.logger.WithField("phase", phase.Phase).Info("Phase completed successfully")
	}

	// 所有阶段完成
	plan.Status = StatusCompleted
	plan.CurrentPhase = PhaseCompleted
	plan.Progress = 100
	plan.UpdatedAt = time.Now()

	// 通知完成
	for _, handler := range m.eventHandlers {
		if err := handler.OnUpdateCompleted(ctx, plan); err != nil {
			m.logger.WithError(err).Warn("Event handler OnUpdateCompleted failed")
		}
	}

	m.logger.WithField("plan_id", plan.ID).Info("Update execution completed successfully")
	return nil
}

// executePhase 执行具体阶段
func (m *HotUpdateManager) executePhase(ctx context.Context, plan *UpdatePlan, phase *UpdatePhaseInfo) error {
	m.logger.WithField("phase", phase.Phase).Info("Executing phase")

	switch phase.Phase {
	case PhasePreValidation:
		return m.executePreValidation(ctx, plan)
	case PhaseDownload:
		return m.executeDownload(ctx, plan)
	case PhaseValidation:
		return m.executeValidation(ctx, plan)
	case PhaseBackup:
		return m.executeBackup(ctx, plan)
	case PhaseInstall:
		return m.executeInstall(ctx, plan)
	case PhaseTest:
		return m.executeTest(ctx, plan)
	case PhaseActivate:
		return m.executeActivate(ctx, plan)
	case PhaseCleanup:
		return m.executeCleanup(ctx, plan)
	default:
		return fmt.Errorf("unknown phase: %s", phase.Phase)
	}
}

// executePreValidation 执行预验证阶段
func (m *HotUpdateManager) executePreValidation(ctx context.Context, plan *UpdatePlan) error {
	m.logger.Info("Starting pre-validation")

	// 执行所有验证器
	for _, validator := range m.validators {
		result, err := validator.ValidateUpdate(ctx, plan)
		if err != nil {
			return fmt.Errorf("validator %s failed: %w", validator.GetValidatorName(), err)
		}

		plan.PreValidationResults = append(plan.PreValidationResults, result)

		// 如果关键验证失败，停止执行
		if result.Critical && !result.Success {
			return fmt.Errorf("critical validation failed: %s - %s", result.CheckName, result.Message)
		}
	}

	return nil
}

// executeDownload 执行下载阶段
func (m *HotUpdateManager) executeDownload(ctx context.Context, plan *UpdatePlan) error {
	m.logger.Info("Starting download")

	// 如果是本地文件，跳过下载
	if !isURL(plan.Request.PackageURL) {
		m.logger.Info("Package is local file, skipping download")
		return nil
	}

	// 创建下载目录
	downloadDir := filepath.Join(m.config.DownloadDirectory, plan.ID)
	if err := os.MkdirAll(downloadDir, 0755); err != nil {
		return fmt.Errorf("failed to create download directory: %w", err)
	}

	// 下载文件
	fileName := fmt.Sprintf("%s-%s.plugin", plan.Request.PluginName, plan.Request.TargetVersion)
	downloadPath := filepath.Join(downloadDir, fileName)

	if err := m.downloadFile(ctx, plan.Request.PackageURL, downloadPath); err != nil {
		return fmt.Errorf("failed to download package: %w", err)
	}

	// 更新包路径
	plan.Request.PackageURL = downloadPath

	m.logger.WithField("path", downloadPath).Info("Package downloaded successfully")
	return nil
}

// executeValidation 执行验证阶段
func (m *HotUpdateManager) executeValidation(ctx context.Context, plan *UpdatePlan) error {
	m.logger.Info("Starting validation")

	// 执行校验和验证器（如果没有在预验证中执行）
	checksumValidator := &ChecksumValidator{logger: m.logger}
	result, err := checksumValidator.ValidateUpdate(ctx, plan)
	if err != nil {
		return fmt.Errorf("checksum validation failed: %w", err)
	}

	if !result.Success {
		return fmt.Errorf("checksum validation failed: %s", result.Message)
	}

	// 执行签名验证器（如果启用）
	if m.config.VerifySignature {
		signatureValidator := &SignatureValidator{logger: m.logger}
		result, err := signatureValidator.ValidateUpdate(ctx, plan)
		if err != nil {
			return fmt.Errorf("signature validation failed: %w", err)
		}

		if !result.Success {
			return fmt.Errorf("signature validation failed: %s", result.Message)
		}
	}

	return nil
}

// executeBackup 执行备份阶段
func (m *HotUpdateManager) executeBackup(ctx context.Context, plan *UpdatePlan) error {
	m.logger.Info("Starting backup")

	if !m.config.EnableBackup {
		m.logger.Info("Backup disabled, skipping")
		return nil
	}

	// 创建备份目录
	backupDir := filepath.Join(m.config.BackupDirectory, plan.ID)
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("failed to create backup directory: %w", err)
	}

	// 备份当前插件
	currentPluginPath := filepath.Join("/plugins", plan.Request.PluginName) // 简化路径
	backupPath := filepath.Join(backupDir, fmt.Sprintf("%s-%s.backup", plan.Request.PluginName, plan.Request.CurrentVersion))

	if err := m.copyFile(currentPluginPath, backupPath); err != nil {
		return fmt.Errorf("failed to backup current plugin: %w", err)
	}

	// 记录备份信息
	plan.BackupInfo = &BackupInfo{
		ID:         plan.ID,
		PluginName: plan.Request.PluginName,
		Version:    plan.Request.CurrentVersion,
		BackupPath: backupPath,
		CreatedAt:  time.Now(),
	}

	// 计算备份文件大小
	if stat, err := os.Stat(backupPath); err == nil {
		plan.BackupInfo.Size = stat.Size()
	}

	m.logger.WithField("backup_path", backupPath).Info("Backup completed successfully")
	return nil
}

// executeInstall 执行安装阶段
func (m *HotUpdateManager) executeInstall(ctx context.Context, plan *UpdatePlan) error {
	m.logger.Info("Starting installation")

	// 获取目标安装路径
	installPath := filepath.Join("/plugins", plan.Request.PluginName) // 简化路径

	// 复制新插件文件
	if err := m.copyFile(plan.Request.PackageURL, installPath); err != nil {
		return fmt.Errorf("failed to install new plugin: %w", err)
	}

	// 设置权限
	if err := os.Chmod(installPath, 0755); err != nil {
		return fmt.Errorf("failed to set plugin permissions: %w", err)
	}

	m.logger.WithField("install_path", installPath).Info("Plugin installed successfully")
	return nil
}

// executeTest 执行测试阶段
func (m *HotUpdateManager) executeTest(ctx context.Context, plan *UpdatePlan) error {
	m.logger.Info("Starting testing")

	// 简化的测试逻辑
	// 实际应该加载插件并执行测试用例

	// 检查插件文件是否可执行
	installPath := filepath.Join("/plugins", plan.Request.PluginName)
	if _, err := os.Stat(installPath); err != nil {
		return fmt.Errorf("installed plugin not found: %w", err)
	}

	// 这里应该添加更多的测试逻辑，比如：
	// 1. 加载插件
	// 2. 验证插件接口
	// 3. 执行基本功能测试
	// 4. 检查性能指标

	m.logger.Info("Plugin testing completed successfully")
	return nil
}

// executeActivate 执行激活阶段
func (m *HotUpdateManager) executeActivate(ctx context.Context, plan *UpdatePlan) error {
	m.logger.Info("Starting activation")

	// 这里应该将新插件注册到插件注册表
	// 并停止旧版本的插件

	// 简化实现：记录激活状态
	plan.ActivatedAt = &[]time.Time{time.Now()}[0]

	m.logger.Info("Plugin activated successfully")
	return nil
}

// executeCleanup 执行清理阶段
func (m *HotUpdateManager) executeCleanup(ctx context.Context, plan *UpdatePlan) error {
	m.logger.Info("Starting cleanup")

	// 清理下载文件
	if plan.Request.PackageURL != "" && isURL(plan.Request.PackageURL) {
		downloadDir := filepath.Join(m.config.DownloadDirectory, plan.ID)
		if err := os.RemoveAll(downloadDir); err != nil {
			m.logger.WithError(err).Warn("Failed to cleanup download directory")
		}
	}

	// 清理临时文件
	tempDir := filepath.Join("/tmp", "plugin_update_"+plan.ID)
	if err := os.RemoveAll(tempDir); err != nil {
		m.logger.WithError(err).Warn("Failed to cleanup temp directory")
	}

	m.logger.Info("Cleanup completed")
	return nil
}

// handlePhaseFailure 处理阶段失败
func (m *HotUpdateManager) handlePhaseFailure(ctx context.Context, plan *UpdatePlan, phase UpdatePhase, err error) error {
	m.logger.WithFields(logrus.Fields{
		"phase": phase,
		"error": err,
	}).Error("Phase execution failed")

	plan.Status = StatusFailed
	plan.UpdatedAt = time.Now()

	// 通知失败
	for _, handler := range m.eventHandlers {
		if handlerErr := handler.OnUpdateFailed(ctx, plan, err); handlerErr != nil {
			m.logger.WithError(handlerErr).Warn("Event handler OnUpdateFailed failed")
		}
	}

	// 如果可以回滚，执行回滚
	if phase != PhasePreValidation && plan.BackupInfo != nil {
		m.logger.Info("Attempting rollback due to phase failure")
		if rollbackErr := m.rollbackUpdate(ctx, plan); rollbackErr != nil {
			m.logger.WithError(rollbackErr).Error("Rollback failed")
			return fmt.Errorf("phase failed and rollback failed: phase error: %w, rollback error: %v", err, rollbackErr)
		}
	}

	return err
}

// rollbackUpdate 回滚更新
func (m *HotUpdateManager) rollbackUpdate(ctx context.Context, plan *UpdatePlan) error {
	m.logger.WithField("plan_id", plan.ID).Info("Starting rollback")

	// 通知回滚开始
	for _, handler := range m.eventHandlers {
		if err := handler.OnRollbackStarted(ctx, plan); err != nil {
			m.logger.WithError(err).Warn("Event handler OnRollbackStarted failed")
		}
	}

	plan.Status = StatusRollingBack
	plan.CurrentPhase = PhaseRollback
	plan.UpdatedAt = time.Now()

	// 如果有备份，恢复备份
	if plan.BackupInfo != nil {
		installPath := filepath.Join("/plugins", plan.Request.PluginName)
		if err := m.copyFile(plan.BackupInfo.BackupPath, installPath); err != nil {
			return fmt.Errorf("failed to restore from backup: %w", err)
		}

		m.logger.Info("Restored plugin from backup")
	}

	// 清理已安装的新版本文件
	// 这里的逻辑取决于具体的安装过程

	plan.Status = StatusRolledBack
	plan.UpdatedAt = time.Now()

	m.logger.WithField("plan_id", plan.ID).Info("Rollback completed successfully")
	return nil
}

// ==================== 辅助方法 ====================

// downloadFile 下载文件
func (m *HotUpdateManager) downloadFile(ctx context.Context, url, destPath string) error {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: m.config.DefaultTimeout,
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return err
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("download failed with status: %s", resp.Status)
	}

	// 创建目标文件
	file, err := os.Create(destPath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 复制数据
	_, err = io.Copy(file, resp.Body)
	return err
}

// copyFile 复制文件
func (m *HotUpdateManager) copyFile(src, dst string) error {
	source, err := os.Open(src)
	if err != nil {
		return err
	}
	defer source.Close()

	destination, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destination.Close()

	_, err = io.Copy(destination, source)
	return err
}

// isURL 检查是否为URL
func isURL(str string) bool {
	return len(str) > 7 && (str[:7] == "http://" || str[:8] == "https://")
}
