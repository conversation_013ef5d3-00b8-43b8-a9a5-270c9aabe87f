// 插件热更新管理器
package hotupdate

import (
	"aiops/plugins/dependency"
	plugininterface "aiops/plugins/interface"
	"aiops/plugins/security"
	"context"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"sort"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ==================== 类型定义 ====================

// UpdateStrategy 更新策略
type UpdateStrategy string

const (
	StrategyRolling   UpdateStrategy = "rolling"    // 滚动更新
	StrategyBlueGreen UpdateStrategy = "blue_green" // 蓝绿部署
	StrategyCanary    UpdateStrategy = "canary"     // 金丝雀发布
	StrategyImmediate UpdateStrategy = "immediate"  // 立即更新
)

// UpdatePhase 更新阶段
type UpdatePhase string

const (
	PhasePreValidation UpdatePhase = "pre_validation"
	PhaseDownload      UpdatePhase = "download"
	PhaseValidation    UpdatePhase = "validation"
	PhaseBackup        UpdatePhase = "backup"
	PhaseInstall       UpdatePhase = "install"
	PhaseTest          UpdatePhase = "test"
	PhaseActivate      UpdatePhase = "activate"
	PhaseCleanup       UpdatePhase = "cleanup"
	PhaseCompleted     UpdatePhase = "completed"
	PhaseFailed        UpdatePhase = "failed"
	PhaseRollback      UpdatePhase = "rollback"
)

// UpdateStatus 更新状态
type UpdateStatus string

const (
	StatusPending    UpdateStatus = "pending"
	StatusInProgress UpdateStatus = "in_progress"
	StatusCompleted  UpdateStatus = "completed"
	StatusFailed     UpdateStatus = "failed"
	StatusRolledBack UpdateStatus = "rolled_back"
	StatusCancelled  UpdateStatus = "cancelled"
)

// UpdateRequest 更新请求
type UpdateRequest struct {
	PluginName     string                 `json:"plugin_name"`
	CurrentVersion string                 `json:"current_version"`
	TargetVersion  string                 `json:"target_version"`
	Strategy       UpdateStrategy         `json:"strategy"`
	Config         map[string]interface{} `json:"config"`

	// 包信息
	PackageURL         string `json:"package_url"`
	Checksum           string `json:"checksum,omitempty"`
	TargetOS           string `json:"target_os,omitempty"`
	TargetArch         string `json:"target_arch,omitempty"`
	MinGoVersion       string `json:"min_go_version,omitempty"`
	RequiredAPIVersion string `json:"required_api_version,omitempty"`

	// 更新选项
	ForceUpdate     bool          `json:"force_update"`
	BackupData      bool          `json:"backup_data"`
	TestTimeout     time.Duration `json:"test_timeout"`
	RollbackOnError bool          `json:"rollback_on_error"`
	SkipOptional    bool          `json:"skip_optional"`

	// 验证选项
	SkipValidation bool     `json:"skip_validation"`
	RequiredChecks []string `json:"required_checks"`

	// 调度选项
	ScheduledTime   *time.Time `json:"scheduled_time,omitempty"`
	MaintenanceMode bool       `json:"maintenance_mode"`
}

// UpdatePlan 更新计划
type UpdatePlan struct {
	ID        string         `json:"id"`
	Request   *UpdateRequest `json:"request"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`

	// 计划详情
	Phases          []UpdatePhaseInfo                   `json:"phases"`
	Dependencies    []*plugininterface.PluginDependency `json:"dependencies"`
	AffectedPlugins []string                            `json:"affected_plugins"`
	EstimatedTime   time.Duration                       `json:"estimated_time"`

	// 资源需求
	ResourceRequirements *ResourceRequirements `json:"resource_requirements"`

	// 验证结果
	PreValidationResults []*ValidationResult `json:"pre_validation_results"`

	Status       UpdateStatus `json:"status"`
	CurrentPhase UpdatePhase  `json:"current_phase"`
	Progress     float64      `json:"progress"`
	Message      string       `json:"message"`
	Error        string       `json:"error,omitempty"`
}

// UpdatePhaseInfo 更新阶段信息
type UpdatePhaseInfo struct {
	Phase         UpdatePhase   `json:"phase"`
	Name          string        `json:"name"`
	Description   string        `json:"description"`
	EstimatedTime time.Duration `json:"estimated_time"`
	Required      bool          `json:"required"`
	CanRollback   bool          `json:"can_rollback"`
	Status        UpdateStatus  `json:"status"`
	StartTime     *time.Time    `json:"start_time,omitempty"`
	EndTime       *time.Time    `json:"end_time,omitempty"`
	Error         string        `json:"error,omitempty"`
}

// ResourceRequirements 资源需求
type ResourceRequirements struct {
	CPU         float64       `json:"cpu"`
	Memory      int64         `json:"memory"`
	DiskSpace   int64         `json:"disk_space"`
	NetworkBW   int64         `json:"network_bandwidth"`
	Downtime    time.Duration `json:"downtime"`
	Parallelism int           `json:"parallelism"`
}

// ValidationResult 验证结果
type ValidationResult struct {
	CheckName string    `json:"check_name"`
	Success   bool      `json:"success"`
	Message   string    `json:"message"`
	Details   string    `json:"details,omitempty"`
	Timestamp time.Time `json:"timestamp"`
	Critical  bool      `json:"critical"`
}

// BackupInfo 备份信息
type BackupInfo struct {
	ID          string    `json:"id"`
	PluginName  string    `json:"plugin_name"`
	Version     string    `json:"version"`
	BackupPath  string    `json:"backup_path"`
	Size        int64     `json:"size"`
	Checksum    string    `json:"checksum"`
	CreatedAt   time.Time `json:"created_at"`
	Description string    `json:"description"`
}

// ==================== 热更新管理器 ====================

// HotUpdateManager 热更新管理器
type HotUpdateManager struct {
	mutex           sync.RWMutex
	logger          *logrus.Logger
	securityManager *security.SecurityManager
	depManager      *dependency.DependencyManager

	// 配置
	config *HotUpdateConfig

	// 状态管理
	updatePlans   map[string]*UpdatePlan
	activeUpdates map[string]*UpdatePlan
	backups       map[string]*BackupInfo

	// 验证器
	validators map[string]UpdateValidator

	// 事件处理
	eventHandlers []UpdateEventHandler

	// 工作目录
	workDir     string
	backupDir   string
	downloadDir string

	// 停止信号
	ctx    context.Context
	cancel context.CancelFunc
}

// HotUpdateConfig 热更新配置
type HotUpdateConfig struct {
	WorkDirectory        string        `json:"work_directory"`
	BackupDirectory      string        `json:"backup_directory"`
	DownloadDirectory    string        `json:"download_directory"`
	MaxConcurrentUpdates int           `json:"max_concurrent_updates"`
	DefaultTimeout       time.Duration `json:"default_timeout"`
	RetryAttempts        int           `json:"retry_attempts"`
	RetryDelay           time.Duration `json:"retry_delay"`
	EnableBackup         bool          `json:"enable_backup"`
	BackupRetention      time.Duration `json:"backup_retention"`
	VerifySignature      bool          `json:"verify_signature"`
	RequireChecksum      bool          `json:"require_checksum"`
}

// UpdateValidator 更新验证器接口
type UpdateValidator interface {
	ValidateUpdate(ctx context.Context, plan *UpdatePlan) (*ValidationResult, error)
	GetValidatorName() string
}

// UpdateEventHandler 更新事件处理器
type UpdateEventHandler interface {
	OnUpdateStarted(ctx context.Context, plan *UpdatePlan) error
	OnPhaseChanged(ctx context.Context, plan *UpdatePlan, phase UpdatePhase) error
	OnUpdateCompleted(ctx context.Context, plan *UpdatePlan) error
	OnUpdateFailed(ctx context.Context, plan *UpdatePlan, err error) error
	OnRollbackStarted(ctx context.Context, plan *UpdatePlan) error
}

// NewHotUpdateManager 创建热更新管理器
func NewHotUpdateManager(
	config *HotUpdateConfig,
	securityManager *security.SecurityManager,
	depManager *dependency.DependencyManager,
	logger *logrus.Logger,
) (*HotUpdateManager, error) {
	if config == nil {
		config = &HotUpdateConfig{
			WorkDirectory:        "/tmp/plugin_updates",
			BackupDirectory:      "/tmp/plugin_backups",
			DownloadDirectory:    "/tmp/plugin_downloads",
			MaxConcurrentUpdates: 3,
			DefaultTimeout:       30 * time.Minute,
			RetryAttempts:        3,
			RetryDelay:           5 * time.Second,
			EnableBackup:         true,
			BackupRetention:      30 * 24 * time.Hour, // 30天
			VerifySignature:      true,
			RequireChecksum:      true,
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	manager := &HotUpdateManager{
		logger:          logger,
		securityManager: securityManager,
		depManager:      depManager,
		config:          config,
		updatePlans:     make(map[string]*UpdatePlan),
		activeUpdates:   make(map[string]*UpdatePlan),
		backups:         make(map[string]*BackupInfo),
		validators:      make(map[string]UpdateValidator),
		eventHandlers:   make([]UpdateEventHandler, 0),
		workDir:         config.WorkDirectory,
		backupDir:       config.BackupDirectory,
		downloadDir:     config.DownloadDirectory,
		ctx:             ctx,
		cancel:          cancel,
	}

	// 创建工作目录
	if err := manager.ensureDirectories(); err != nil {
		return nil, fmt.Errorf("failed to create directories: %w", err)
	}

	// 注册默认验证器
	manager.registerDefaultValidators()

	// 启动后台任务
	go manager.backgroundTasks()

	return manager, nil
}

// ensureDirectories 确保工作目录存在
func (m *HotUpdateManager) ensureDirectories() error {
	dirs := []string{m.workDir, m.backupDir, m.downloadDir}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}
	return nil
}

// registerDefaultValidators 注册默认验证器
func (m *HotUpdateManager) registerDefaultValidators() {
	// 签名验证器
	if m.config.VerifySignature {
		m.RegisterValidator(&SignatureValidator{
			securityManager: m.securityManager,
			logger:          m.logger,
		})
	}

	// 校验和验证器
	if m.config.RequireChecksum {
		m.RegisterValidator(&ChecksumValidator{
			logger: m.logger,
		})
	}

	// 依赖验证器
	m.RegisterValidator(&DependencyValidator{
		depManager: m.depManager,
		logger:     m.logger,
	})

	// 兼容性验证器
	m.RegisterValidator(&CompatibilityValidator{
		logger: m.logger,
	})

	// 资源验证器
	m.RegisterValidator(&ResourceValidator{
		logger: m.logger,
	})

	// 平台验证器
	m.RegisterValidator(&PlatformValidator{
		logger: m.logger,
	})
}

// ==================== 公共接口 ====================

// CreateUpdatePlan 创建更新计划
func (m *HotUpdateManager) CreateUpdatePlan(request *UpdateRequest) (*UpdatePlan, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 验证请求
	if err := m.validateUpdateRequest(request); err != nil {
		return nil, fmt.Errorf("invalid update request: %w", err)
	}

	// 生成计划ID
	planID := m.generatePlanID(request)

	// 检查是否已存在
	if _, exists := m.updatePlans[planID]; exists {
		return nil, fmt.Errorf("update plan already exists for %s %s->%s",
			request.PluginName, request.CurrentVersion, request.TargetVersion)
	}

	// 创建更新计划
	plan := &UpdatePlan{
		ID:           planID,
		Request:      request,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		Status:       StatusPending,
		CurrentPhase: PhasePreValidation,
		Progress:     0,
	}

	// 生成更新阶段
	plan.Phases = m.generateUpdatePhases(request)

	// 分析依赖关系
	dependencies, err := m.analyzeDependencies(request.PluginName, request.TargetVersion)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze dependencies: %w", err)
	}
	plan.Dependencies = dependencies

	// 计算受影响的插件
	affectedPlugins, err := m.calculateAffectedPlugins(request.PluginName)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate affected plugins: %w", err)
	}
	plan.AffectedPlugins = affectedPlugins

	// 估算时间和资源
	plan.EstimatedTime = m.estimateUpdateTime(request)
	plan.ResourceRequirements = m.calculateResourceRequirements(request)

	// 执行预验证
	if !request.SkipValidation {
		validationResults, err := m.performPreValidation(m.ctx, plan)
		if err != nil {
			plan.Status = StatusFailed
			plan.Error = err.Error()
			return plan, fmt.Errorf("pre-validation failed: %w", err)
		}
		plan.PreValidationResults = validationResults

		// 检查关键验证是否通过
		for _, result := range validationResults {
			if result.Critical && !result.Success {
				plan.Status = StatusFailed
				plan.Error = fmt.Sprintf("critical validation failed: %s", result.Message)
				return plan, fmt.Errorf("critical validation failed: %s", result.Message)
			}
		}
	}

	// 保存计划
	m.updatePlans[planID] = plan

	m.logger.WithFields(logrus.Fields{
		"plan_id": planID,
		"plugin":  request.PluginName,
		"version": fmt.Sprintf("%s->%s", request.CurrentVersion, request.TargetVersion),
	}).Info("Update plan created successfully")

	return plan, nil
}

// ExecuteUpdate 执行更新
func (m *HotUpdateManager) ExecuteUpdate(planID string) error {
	m.mutex.Lock()
	plan, exists := m.updatePlans[planID]
	if !exists {
		m.mutex.Unlock()
		return fmt.Errorf("update plan not found: %s", planID)
	}

	// 检查并发限制
	if len(m.activeUpdates) >= m.config.MaxConcurrentUpdates {
		m.mutex.Unlock()
		return fmt.Errorf("maximum concurrent updates reached (%d)", m.config.MaxConcurrentUpdates)
	}

	// 检查计划状态
	if plan.Status != StatusPending {
		m.mutex.Unlock()
		return fmt.Errorf("invalid plan status: %s", plan.Status)
	}

	// 标记为活跃更新
	m.activeUpdates[planID] = plan
	plan.Status = StatusInProgress
	plan.UpdatedAt = time.Now()
	m.mutex.Unlock()

	// 异步执行更新
	go func() {
		defer func() {
			m.mutex.Lock()
			delete(m.activeUpdates, planID)
			m.mutex.Unlock()
		}()

		if err := m.executeUpdatePlan(m.ctx, plan); err != nil {
			m.logger.WithFields(logrus.Fields{
				"plan_id": planID,
				"error":   err.Error(),
			}).Error("Update execution failed")

			// 触发回滚
			if plan.Request.RollbackOnError {
				if rollbackErr := m.rollbackUpdate(m.ctx, plan); rollbackErr != nil {
					m.logger.WithFields(logrus.Fields{
						"plan_id": planID,
						"error":   rollbackErr.Error(),
					}).Error("Rollback failed")
				}
			}
		}
	}()

	return nil
}

// GetUpdatePlan 获取更新计划
func (m *HotUpdateManager) GetUpdatePlan(planID string) (*UpdatePlan, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	plan, exists := m.updatePlans[planID]
	if !exists {
		return nil, fmt.Errorf("update plan not found: %s", planID)
	}

	return plan, nil
}

// ListUpdatePlans 列出更新计划
func (m *HotUpdateManager) ListUpdatePlans() []*UpdatePlan {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	plans := make([]*UpdatePlan, 0, len(m.updatePlans))
	for _, plan := range m.updatePlans {
		plans = append(plans, plan)
	}

	// 按创建时间排序
	sort.Slice(plans, func(i, j int) bool {
		return plans[i].CreatedAt.After(plans[j].CreatedAt)
	})

	return plans
}

// CancelUpdate 取消更新
func (m *HotUpdateManager) CancelUpdate(planID string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	plan, exists := m.updatePlans[planID]
	if !exists {
		return fmt.Errorf("update plan not found: %s", planID)
	}

	if plan.Status != StatusPending && plan.Status != StatusInProgress {
		return fmt.Errorf("cannot cancel update in status: %s", plan.Status)
	}

	plan.Status = StatusCancelled
	plan.UpdatedAt = time.Now()

	// 从活跃更新中移除
	delete(m.activeUpdates, planID)

	m.logger.WithFields(logrus.Fields{
		"plan_id": planID,
	}).Info("Update cancelled")

	return nil
}

// RollbackUpdate 回滚更新
func (m *HotUpdateManager) RollbackUpdate(planID string) error {
	plan, err := m.GetUpdatePlan(planID)
	if err != nil {
		return err
	}

	return m.rollbackUpdate(m.ctx, plan)
}

// RegisterValidator 注册验证器
func (m *HotUpdateManager) RegisterValidator(validator UpdateValidator) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.validators[validator.GetValidatorName()] = validator
}

// AddEventHandler 添加事件处理器
func (m *HotUpdateManager) AddEventHandler(handler UpdateEventHandler) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.eventHandlers = append(m.eventHandlers, handler)
}

// ==================== 内部实现 ====================

// calculateChecksum 计算文件校验和
func (m *HotUpdateManager) calculateChecksum(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// backgroundTasks 后台任务
func (m *HotUpdateManager) backgroundTasks() {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.cleanupOldBackups()
			m.cleanupCompletedPlans()
		}
	}
}

// cleanupOldBackups 清理旧备份
func (m *HotUpdateManager) cleanupOldBackups() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	cutoff := time.Now().Add(-m.config.BackupRetention)
	for id, backup := range m.backups {
		if backup.CreatedAt.Before(cutoff) {
			if err := os.RemoveAll(backup.BackupPath); err != nil {
				m.logger.WithError(err).Warn("Failed to remove old backup")
				continue
			}
			delete(m.backups, id)
			m.logger.WithField("backup_id", id).Info("Removed old backup")
		}
	}
}

// cleanupCompletedPlans 清理已完成的计划
func (m *HotUpdateManager) cleanupCompletedPlans() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	cutoff := time.Now().Add(-24 * time.Hour) // 保留24小时
	for id, plan := range m.updatePlans {
		if (plan.Status == StatusCompleted || plan.Status == StatusFailed || plan.Status == StatusCancelled) &&
			plan.UpdatedAt.Before(cutoff) {
			delete(m.updatePlans, id)
			m.logger.WithField("plan_id", id).Info("Removed completed plan")
		}
	}
}

// Stop 停止热更新管理器
func (m *HotUpdateManager) Stop() error {
	m.cancel()

	// 等待活跃更新完成或超时
	timeout := time.NewTimer(30 * time.Second)
	defer timeout.Stop()

	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout.C:
			m.logger.Warn("Timeout waiting for active updates to complete")
			return nil
		case <-ticker.C:
			m.mutex.RLock()
			activeCount := len(m.activeUpdates)
			m.mutex.RUnlock()

			if activeCount == 0 {
				m.logger.Info("Hot update manager stopped")
				return nil
			}
		}
	}
}
