// 插件容错增强机制
package resilience

import (
	"context"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PluginSafeExecutor 插件安全执行器
type PluginSafeExecutor struct {
	logger          *zap.Logger
	crashCount      map[string]int
	crashCountMutex sync.RWMutex
	maxCrashes      int
	recoveryTimeout time.Duration
}

// NewPluginSafeExecutor 创建安全执行器
func NewPluginSafeExecutor(logger *zap.Logger) *PluginSafeExecutor {
	return &PluginSafeExecutor{
		logger:          logger,
		crashCount:      make(map[string]int),
		maxCrashes:      3, // 最多允许3次崩溃
		recoveryTimeout: 30 * time.Second,
	}
}

// SafeExecute 安全执行插件函数
func (e *PluginSafeExecutor) SafeExecute(pluginName string, fn func() error) (err error) {
	defer func() {
		if r := recover(); r != nil {
			// 记录堆栈信息
			stack := debug.Stack()
			e.logger.Error("Plugin panic recovered",
				zap.String("plugin", pluginName),
				zap.Any("panic", r),
				zap.String("stack", string(stack)))

			// 增加崩溃计数
			e.crashCountMutex.Lock()
			e.crashCount[pluginName]++
			crashCount := e.crashCount[pluginName]
			e.crashCountMutex.Unlock()

			// 检查是否需要禁用插件
			if crashCount >= e.maxCrashes {
				e.logger.Error("Plugin disabled due to excessive crashes",
					zap.String("plugin", pluginName),
					zap.Int("crash_count", crashCount))
				err = fmt.Errorf("plugin %s disabled due to excessive crashes", pluginName)
				return
			}

			err = fmt.Errorf("plugin %s panicked: %v", pluginName, r)
		}
	}()

	// 执行函数
	return fn()
}

// SafeExecuteWithTimeout 带超时的安全执行
func (e *PluginSafeExecutor) SafeExecuteWithTimeout(ctx context.Context, pluginName string, timeout time.Duration, fn func() error) error {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	resultCh := make(chan error, 1)

	go func() {
		resultCh <- e.SafeExecute(pluginName, fn)
	}()

	select {
	case err := <-resultCh:
		return err
	case <-ctx.Done():
		return fmt.Errorf("plugin %s execution timeout", pluginName)
	}
}

// ResetCrashCount 重置崩溃计数
func (e *PluginSafeExecutor) ResetCrashCount(pluginName string) {
	e.crashCountMutex.Lock()
	defer e.crashCountMutex.Unlock()
	delete(e.crashCount, pluginName)
}

// GetCrashCount 获取崩溃计数
func (e *PluginSafeExecutor) GetCrashCount(pluginName string) int {
	e.crashCountMutex.RLock()
	defer e.crashCountMutex.RUnlock()
	return e.crashCount[pluginName]
}
