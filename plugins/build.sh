#!/bin/bash

# 插件构建脚本
# 用于将 Go 插件编译为动态库 (.so 文件)

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$SCRIPT_DIR/build"
EXAMPLES_DIR="$SCRIPT_DIR/examples"

# 创建构建目录
mkdir -p "$BUILD_DIR"

echo "开始构建插件..."

# 构建系统指标采集器插件
echo "构建 system-collector 插件..."
cd "$EXAMPLES_DIR/system-collector"
go build -buildmode=plugin -o "$BUILD_DIR/system-collector.so" main.go
echo "✓ system-collector.so 构建完成"

# 构建 MySQL 采集器插件
echo "构建 mysql-collector 插件..."
cd "$EXAMPLES_DIR/mysql-collector"
go build -buildmode=plugin -o "$BUILD_DIR/mysql-collector.so" main.go
echo "✓ mysql-collector.so 构建完成"

# 构建邮件告警插件
if [ -d "$EXAMPLES_DIR/email-alerter" ]; then
    echo "构建 email-alerter 插件..."
    cd "$EXAMPLES_DIR/email-alerter"
    go build -buildmode=plugin -o "$BUILD_DIR/email-alerter.so" main.go
    echo "✓ email-alerter.so 构建完成"
fi

# 构建简单分析器插件
if [ -d "$EXAMPLES_DIR/simple-analyzer" ]; then
    echo "构建 simple-analyzer 插件..."
    cd "$EXAMPLES_DIR/simple-analyzer"
    go build -buildmode=plugin -o "$BUILD_DIR/simple-analyzer.so" main.go
    echo "✓ simple-analyzer.so 构建完成"
fi

# 构建增强分析器插件
if [ -d "$EXAMPLES_DIR/enhanced-analyzer" ]; then
    echo "构建 enhanced-analyzer 插件..."
    cd "$EXAMPLES_DIR/enhanced-analyzer"
    go build -buildmode=plugin -o "$BUILD_DIR/enhanced-analyzer.so" main.go
    echo "✓ enhanced-analyzer.so 构建完成"
fi

echo ""
echo "所有插件构建完成！"
echo "插件文件位置: $BUILD_DIR"
echo ""
ls -la "$BUILD_DIR"/*.so
