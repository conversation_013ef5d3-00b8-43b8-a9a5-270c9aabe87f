package loader

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"plugin"
	"sync"

	plugininterface "aiops/plugins/interface"
	"aiops/plugins/registry"
)

// PluginLoader 插件加载器
type PluginLoader struct {
	registry   *registry.PluginRegistry
	pluginDirs []string
	mu         sync.RWMutex
}

// LoaderConfig 加载器配置
type LoaderConfig struct {
	PluginDirs []string `yaml:"plugin_dirs"`
	AutoLoad   bool     `yaml:"auto_load"`
	WatchMode  bool     `yaml:"watch_mode"`
}

// NewPluginLoader 创建新的插件加载器
func NewPluginLoader(registry *registry.PluginRegistry, config *LoaderConfig) *PluginLoader {
	return &PluginLoader{
		registry:   registry,
		pluginDirs: config.PluginDirs,
	}
}

// LoadPluginFromFile 从文件加载插件
func (l *PluginLoader) LoadPluginFromFile(filepath string) error {
	l.mu.Lock()
	defer l.mu.Unlock()

	// 加载动态库
	p, err := plugin.Open(filepath)
	if err != nil {
		return fmt.Errorf("failed to open plugin file %s: %w", filepath, err)
	}

	// 查找插件信息函数
	infoSymbol, err := p.Lookup("GetPluginInfo")
	if err != nil {
		return fmt.Errorf("plugin %s missing GetPluginInfo function: %w", filepath, err)
	}

	infoFunc, ok := infoSymbol.(func() *plugininterface.PluginInfo)
	if !ok {
		return fmt.Errorf("invalid GetPluginInfo function signature in plugin %s", filepath)
	}

	// 获取插件信息
	info := infoFunc()
	if info == nil {
		return fmt.Errorf("plugin %s returned nil info", filepath)
	}

	// 查找插件工厂函数
	factorySymbol, err := p.Lookup("CreatePluginFactory")
	if err != nil {
		return fmt.Errorf("plugin %s missing CreatePluginFactory function: %w", filepath, err)
	}

	factoryFunc, ok := factorySymbol.(func() plugininterface.PluginFactory)
	if !ok {
		return fmt.Errorf("invalid CreatePluginFactory function signature in plugin %s", filepath)
	}

	// 创建插件工厂
	factory := factoryFunc()
	if factory == nil {
		return fmt.Errorf("plugin %s returned nil factory", filepath)
	}

	// 注册插件
	if err := l.registry.Register(info, factory); err != nil {
		return fmt.Errorf("failed to register plugin %s: %w", info.Name, err)
	}

	return nil
}

// LoadPluginsFromDir 从目录加载插件
func (l *PluginLoader) LoadPluginsFromDir(dir string) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return fmt.Errorf("plugin directory %s does not exist", dir)
	}

	return filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理 .so 文件 (动态库)
		if filepath.Ext(path) == ".so" {
			if err := l.LoadPluginFromFile(path); err != nil {
				// 记录错误但继续加载其他插件
				fmt.Printf("Failed to load plugin %s: %v\n", path, err)
			}
		}

		return nil
	})
}

// LoadAllPlugins 加载所有插件目录中的插件
func (l *PluginLoader) LoadAllPlugins() error {
	for _, dir := range l.pluginDirs {
		if err := l.LoadPluginsFromDir(dir); err != nil {
			return fmt.Errorf("failed to load plugins from directory %s: %w", dir, err)
		}
	}
	return nil
}

// LoadPluginWithConfig 加载并配置插件
func (l *PluginLoader) LoadPluginWithConfig(name, version string, config map[string]interface{}) error {
	// 加载插件
	if err := l.registry.Load(name, version, config); err != nil {
		return err
	}

	// 启动插件
	if err := l.registry.Start(name, version); err != nil {
		return err
	}

	return nil
}

// UnloadPlugin 卸载插件
func (l *PluginLoader) UnloadPlugin(name, version string) error {
	// 停止插件
	if err := l.registry.Stop(name, version); err != nil {
		// 即使停止失败也尝试注销
		fmt.Printf("Failed to stop plugin %s:%s: %v\n", name, version, err)
	}

	// 注销插件
	return l.registry.Unregister(name, version)
}

// ReloadPlugin 重新加载插件
func (l *PluginLoader) ReloadPlugin(name, version string, config map[string]interface{}) error {
	// 先卸载
	if err := l.UnloadPlugin(name, version); err != nil {
		return fmt.Errorf("failed to unload plugin for reload: %w", err)
	}

	// 重新加载并配置
	return l.LoadPluginWithConfig(name, version, config)
}

// PluginManager 插件管理器
type PluginManager struct {
	loader   *PluginLoader
	registry *registry.PluginRegistry
}

// NewPluginManager 创建新的插件管理器
func NewPluginManager(config *LoaderConfig) *PluginManager {
	registry := registry.NewPluginRegistry()
	loader := NewPluginLoader(registry, config)

	return &PluginManager{
		loader:   loader,
		registry: registry,
	}
}

// Initialize 初始化插件管理器
func (m *PluginManager) Initialize(ctx context.Context) error {
	// 加载所有插件
	if err := m.loader.LoadAllPlugins(); err != nil {
		return fmt.Errorf("failed to load plugins: %w", err)
	}

	// 为所有已注册的插件创建实例并启动
	plugins := m.registry.List()
	for _, plugin := range plugins {
		// 创建插件实例
		if err := m.registry.Load(plugin.Info.Name, plugin.Info.Version, map[string]interface{}{}); err != nil {
			fmt.Printf("Failed to load plugin %s: %v\n", plugin.Info.Name, err)
			continue
		}

		// 启动插件
		if err := m.registry.Start(plugin.Info.Name, plugin.Info.Version); err != nil {
			fmt.Printf("Failed to start plugin %s: %v\n", plugin.Info.Name, err)
			continue
		}
	}

	return nil
}

// GetRegistry 获取插件注册中心
func (m *PluginManager) GetRegistry() *registry.PluginRegistry {
	return m.registry
}

// GetLoader 获取插件加载器
func (m *PluginManager) GetLoader() *PluginLoader {
	return m.loader
}

// GetCollectors 获取所有采集器插件
func (m *PluginManager) GetCollectors() []plugininterface.Collector {
	plugins := m.registry.GetByType(plugininterface.CollectorPlugin)
	collectors := make([]plugininterface.Collector, 0, len(plugins))

	for _, p := range plugins {
		if collector, ok := p.(plugininterface.Collector); ok {
			collectors = append(collectors, collector)
		}
	}

	return collectors
}

// GetProcessors 获取所有处理器插件
func (m *PluginManager) GetProcessors() []plugininterface.Processor {
	plugins := m.registry.GetByType(plugininterface.ProcessorPlugin)
	processors := make([]plugininterface.Processor, 0, len(plugins))

	for _, p := range plugins {
		if processor, ok := p.(plugininterface.Processor); ok {
			processors = append(processors, processor)
		}
	}

	return processors
}

// GetAlerters 获取所有告警器插件
func (m *PluginManager) GetAlerters() []plugininterface.Alerter {
	plugins := m.registry.GetByType(plugininterface.AlerterPlugin)
	alerters := make([]plugininterface.Alerter, 0, len(plugins))

	for _, p := range plugins {
		if alerter, ok := p.(plugininterface.Alerter); ok {
			alerters = append(alerters, alerter)
		}
	}

	return alerters
}

// GetAnalyzers 获取所有分析器插件
func (m *PluginManager) GetAnalyzers() []plugininterface.Analyzer {
	plugins := m.registry.GetByType(plugininterface.AnalyzerPlugin)
	analyzers := make([]plugininterface.Analyzer, 0, len(plugins))

	for _, p := range plugins {
		if analyzer, ok := p.(plugininterface.Analyzer); ok {
			analyzers = append(analyzers, analyzer)
		}
	}

	return analyzers
}
