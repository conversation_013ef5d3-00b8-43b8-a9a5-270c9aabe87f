package main

import (
	"context"
	"fmt"
	"runtime"
	"time"

	pb "aiops/pkg/proto"
	plugininterface "aiops/plugins/interface"
)

// SystemCollectorPlugin 系统指标采集器插件
type SystemCollectorPlugin struct {
	info   *plugininterface.PluginInfo
	config map[string]interface{}
}

// GetInfo 获取插件信息
func (p *SystemCollectorPlugin) GetInfo() *plugininterface.PluginInfo {
	return p.info
}

// Initialize 初始化插件
func (p *SystemCollectorPlugin) Initialize(ctx context.Context, config map[string]interface{}) error {
	p.config = config
	return nil
}

// Start 启动插件
func (p *SystemCollectorPlugin) Start(ctx context.Context) error {
	fmt.Printf("System Collector Plugin started\n")
	return nil
}

// Stop 停止插件
func (p *SystemCollectorPlugin) Stop(ctx context.Context) error {
	fmt.Printf("System Collector Plugin stopped\n")
	return nil
}

// Health 健康检查
func (p *SystemCollectorPlugin) Health(ctx context.Context) error {
	return nil
}

// GetMetrics 获取插件指标
func (p *SystemCollectorPlugin) GetMetrics(ctx context.Context) (map[string]interface{}, error) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return map[string]interface{}{
		"goroutines":   runtime.NumGoroutine(),
		"memory_alloc": m.Alloc,
		"memory_sys":   m.Sys,
		"gc_cycles":    m.NumGC,
	}, nil
}

// GetSupportedDeviceTypes 获取支持的设备类型
func (p *SystemCollectorPlugin) GetSupportedDeviceTypes() []string {
	return []string{"system", "server", "linux", "windows", "macos"}
}

// GetSupportedMetrics 获取支持的指标
func (p *SystemCollectorPlugin) GetSupportedMetrics(deviceType string) ([]*pb.SupportedMetric, error) {
	metrics := []*pb.SupportedMetric{
		{
			MetricKey:     "system.cpu.usage",
			MetricName:    "CPU Usage",
			Description:   "CPU utilization percentage",
			DataType:      "numeric",
			Unit:          "percent",
			IsActive:      true,
			CollectorType: "system",
		},
		{
			MetricKey:     "system.memory.usage",
			MetricName:    "Memory Usage",
			Description:   "Memory utilization percentage",
			DataType:      "numeric",
			Unit:          "percent",
			IsActive:      true,
			CollectorType: "system",
		},
		{
			MetricKey:     "system.memory.available",
			MetricName:    "Available Memory",
			Description:   "Available memory in bytes",
			DataType:      "numeric",
			Unit:          "bytes",
			IsActive:      true,
			CollectorType: "system",
		},
		{
			MetricKey:     "system.disk.usage",
			MetricName:    "Disk Usage",
			Description:   "Disk utilization percentage",
			DataType:      "numeric",
			Unit:          "percent",
			IsActive:      true,
			CollectorType: "system",
		},
		{
			MetricKey:     "system.load.avg1",
			MetricName:    "Load Average 1min",
			Description:   "System load average over 1 minute",
			DataType:      "numeric",
			Unit:          "count",
			IsActive:      true,
			CollectorType: "system",
		},
		{
			MetricKey:     "system.network.bytes_in",
			MetricName:    "Network Bytes In",
			Description:   "Network bytes received per second",
			DataType:      "numeric",
			Unit:          "bytes/sec",
			IsActive:      true,
			CollectorType: "system",
		},
		{
			MetricKey:     "system.network.bytes_out",
			MetricName:    "Network Bytes Out",
			Description:   "Network bytes sent per second",
			DataType:      "numeric",
			Unit:          "bytes/sec",
			IsActive:      true,
			CollectorType: "system",
		},
	}

	return metrics, nil
}

// StartCollection 开始采集
func (p *SystemCollectorPlugin) StartCollection(ctx context.Context, taskConfig *pb.CollectorTaskConfig) error {
	fmt.Printf("Started system collection for task %s on device %s\n", taskConfig.TaskId, taskConfig.DeviceName)
	return nil
}

// StopCollection 停止采集
func (p *SystemCollectorPlugin) StopCollection(ctx context.Context, taskID string) error {
	fmt.Printf("Stopped system collection for task %s\n", taskID)
	return nil
}

// CollectMetrics 采集指标数据
func (p *SystemCollectorPlugin) CollectMetrics(ctx context.Context, taskConfig *pb.CollectorTaskConfig) ([]*pb.MetricData, error) {
	now := time.Now().Unix()

	// 获取系统指标
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 模拟系统指标数据
	metrics := []*pb.MetricData{
		{
			DeviceId:  taskConfig.DeviceId,
			MetricKey: "system.cpu.usage",
			Timestamp: now,
			ValueType: &pb.MetricData_NumericValue{NumericValue: 15.5}, // 模拟 CPU 使用率
			Labels: map[string]string{
				"host": taskConfig.Host,
				"core": "all",
			},
		},
		{
			DeviceId:  taskConfig.DeviceId,
			MetricKey: "system.memory.usage",
			Timestamp: now,
			ValueType: &pb.MetricData_NumericValue{NumericValue: float64(m.Alloc) / float64(m.Sys) * 100},
			Labels: map[string]string{
				"host": taskConfig.Host,
			},
		},
		{
			DeviceId:  taskConfig.DeviceId,
			MetricKey: "system.memory.available",
			Timestamp: now,
			ValueType: &pb.MetricData_NumericValue{NumericValue: float64(m.Sys - m.Alloc)},
			Labels: map[string]string{
				"host": taskConfig.Host,
			},
		},
		{
			DeviceId:  taskConfig.DeviceId,
			MetricKey: "system.disk.usage",
			Timestamp: now,
			ValueType: &pb.MetricData_NumericValue{NumericValue: 68.3}, // 模拟磁盘使用率
			Labels: map[string]string{
				"host":       taskConfig.Host,
				"filesystem": "/",
			},
		},
		{
			DeviceId:  taskConfig.DeviceId,
			MetricKey: "system.load.avg1",
			Timestamp: now,
			ValueType: &pb.MetricData_NumericValue{NumericValue: 1.2}, // 模拟负载
			Labels: map[string]string{
				"host": taskConfig.Host,
			},
		},
	}

	return metrics, nil
}

// ValidateConfig 验证配置
func (p *SystemCollectorPlugin) ValidateConfig(config *pb.CollectorTaskConfig) error {
	if config.DeviceId == "" {
		return fmt.Errorf("device ID is required")
	}

	if config.FrequencySeconds <= 0 {
		return fmt.Errorf("frequency must be positive")
	}

	return nil
}

// GetConfigSchema 获取配置模式
func (p *SystemCollectorPlugin) GetConfigSchema() *plugininterface.ConfigSchema {
	return &plugininterface.ConfigSchema{
		Schema: map[string]any{
			"type": "object",
			"properties": map[string]any{
				"collection_interval": map[string]any{
					"type":        "integer",
					"description": "Collection interval in seconds",
					"default":     30,
					"minimum":     5,
				},
				"enable_cpu": map[string]any{
					"type":        "boolean",
					"description": "Enable CPU metrics collection",
					"default":     true,
				},
				"enable_memory": map[string]any{
					"type":        "boolean",
					"description": "Enable memory metrics collection",
					"default":     true,
				},
				"enable_disk": map[string]any{
					"type":        "boolean",
					"description": "Enable disk metrics collection",
					"default":     true,
				},
				"enable_network": map[string]any{
					"type":        "boolean",
					"description": "Enable network metrics collection",
					"default":     true,
				},
				"disk_paths": map[string]any{
					"type":        "array",
					"description": "Specific disk paths to monitor",
					"items": map[string]any{
						"type": "string",
					},
					"default": []string{"/", "/var", "/home"},
				},
			},
		},
		Required: []string{},
		Defaults: map[string]any{
			"collection_interval": 30,
			"enable_cpu":          true,
			"enable_memory":       true,
			"enable_disk":         true,
			"enable_network":      true,
			"disk_paths":          []string{"/", "/var", "/home"},
		},
		Examples: []map[string]any{
			{
				"collection_interval": 15,
				"enable_cpu":          true,
				"enable_memory":       true,
				"enable_disk":         false,
				"enable_network":      true,
			},
		},
	}
}

// SystemPluginFactory 系统插件工厂
type SystemPluginFactory struct{}

// CreatePlugin 创建插件实例
func (f *SystemPluginFactory) CreatePlugin(pluginType plugininterface.PluginType, config map[string]interface{}) (plugininterface.Plugin, error) {
	if pluginType != plugininterface.CollectorPlugin {
		return nil, fmt.Errorf("unsupported plugin type: %s", pluginType)
	}

	plugin := &SystemCollectorPlugin{
		info: &plugininterface.PluginInfo{
			Name:        "system-collector",
			Version:     "1.0.0",
			Type:        plugininterface.CollectorPlugin,
			Description: "System metrics collector for CPU, memory, disk and network",
			Author:      "DevInsight Team",
			Homepage:    "https://github.com/devinsight/system-collector",
			License:     "MIT",
			Tags:        []string{"system", "cpu", "memory", "disk", "network"},
			CreatedAt:   time.Now(),
		},
	}

	return plugin, nil
}

// CheckCompatibility 检查兼容性
func (f *SystemPluginFactory) CheckCompatibility(systemVersion string) error {
	// System collector is compatible with all versions
	// It only uses standard Go libraries and basic system calls
	return nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *SystemPluginFactory) GetSupportedTypes() []plugininterface.PluginType {
	return []plugininterface.PluginType{plugininterface.CollectorPlugin}
}

// GetPluginInfo 导出函数 - 插件加载器需要
func GetPluginInfo() *plugininterface.PluginInfo {
	return &plugininterface.PluginInfo{
		Name:        "system-collector",
		Version:     "1.0.0",
		Type:        plugininterface.CollectorPlugin,
		Description: "System metrics collector for CPU, memory, disk and network",
		Author:      "DevInsight Team",
		Homepage:    "https://github.com/devinsight/system-collector",
		License:     "MIT",
		Tags:        []string{"system", "cpu", "memory", "disk", "network"},
		CreatedAt:   time.Now(),
	}
}

// CreatePluginFactory 导出函数 - 插件加载器需要
func CreatePluginFactory() plugininterface.PluginFactory {
	return &SystemPluginFactory{}
}

func main() {
	// 这个文件会被编译为动态库(.so文件)
	// 不需要main函数，但为了编译通过而保留
}
