package main

import (
	"context"
	"fmt"
	"log"
	"net/smtp"
	"time"

	plugininterface "aiops/plugins/interface"
)

// EmailAlerterPlugin 邮件告警插件
type EmailAlerterPlugin struct {
	info       *plugininterface.PluginInfo
	config     map[string]interface{}
	smtpServer string
	smtpPort   int
	username   string
	password   string
	from       string
	to         []string
}

// GetInfo 获取插件信息
func (p *EmailAlerterPlugin) GetInfo() *plugininterface.PluginInfo {
	return p.info
}

// Initialize 初始化插件
func (p *EmailAlerterPlugin) Initialize(ctx context.Context, config map[string]interface{}) error {
	p.config = config

	// 从配置中读取 SMTP 设置
	if server, ok := config["smtp_server"].(string); ok {
		p.smtpServer = server
	} else {
		p.smtpServer = "smtp.gmail.com"
	}

	if port, ok := config["smtp_port"].(int); ok {
		p.smtpPort = port
	} else {
		p.smtpPort = 587
	}

	if username, ok := config["username"].(string); ok {
		p.username = username
	}

	if password, ok := config["password"].(string); ok {
		p.password = password
	}

	if from, ok := config["from"].(string); ok {
		p.from = from
	} else {
		p.from = p.username
	}

	if to, ok := config["to"].([]string); ok {
		p.to = to
	}

	return nil
}

// Start 启动插件
func (p *EmailAlerterPlugin) Start(ctx context.Context) error {
	fmt.Printf("Email Alerter Plugin started\n")
	return nil
}

// Stop 停止插件
func (p *EmailAlerterPlugin) Stop(ctx context.Context) error {
	fmt.Printf("Email Alerter Plugin stopped\n")
	return nil
}

// Health 健康检查
func (p *EmailAlerterPlugin) Health(ctx context.Context) error {
	// 尝试连接 SMTP 服务器
	addr := fmt.Sprintf("%s:%d", p.smtpServer, p.smtpPort)
	auth := smtp.PlainAuth("", p.username, p.password, p.smtpServer)

	// 创建连接测试
	client, err := smtp.Dial(addr)
	if err != nil {
		return fmt.Errorf("failed to connect to SMTP server: %w", err)
	}
	defer client.Close()

	if err := client.Auth(auth); err != nil {
		return fmt.Errorf("SMTP authentication failed: %w", err)
	}

	return nil
}

// GetMetrics 获取插件指标
func (p *EmailAlerterPlugin) GetMetrics(ctx context.Context) (map[string]interface{}, error) {
	return map[string]any{
		"smtp_server": p.smtpServer,
		"smtp_port":   p.smtpPort,
		"from":        p.from,
		"to_count":    len(p.to),
	}, nil
}

// GetConfigSchema 获取配置模式
func (p *EmailAlerterPlugin) GetConfigSchema() *plugininterface.ConfigSchema {
	return &plugininterface.ConfigSchema{
		Schema: map[string]any{
			"type": "object",
			"properties": map[string]any{
				"username": map[string]any{
					"type":        "string",
					"description": "SMTP服务器用户名",
					"example":     "<EMAIL>",
				},
				"password": map[string]any{
					"type":        "string",
					"description": "SMTP服务器密码",
					"example":     "your-password",
				},
				"smtp_server": map[string]any{
					"type":        "string",
					"description": "SMTP服务器地址",
					"default":     "smtp.gmail.com",
					"example":     "smtp.gmail.com",
				},
				"smtp_port": map[string]any{
					"type":        "integer",
					"description": "SMTP服务器端口",
					"default":     587,
					"example":     587,
				},
				"from": map[string]any{
					"type":        "string",
					"description": "发件人邮箱地址",
					"example":     "<EMAIL>",
				},
				"to": map[string]any{
					"type":        "array",
					"description": "收件人邮箱地址列表",
					"items": map[string]any{
						"type": "string",
					},
					"example": []string{"<EMAIL>", "<EMAIL>"},
				},
			},
		},
		Required: []string{"username", "password", "smtp_server", "smtp_port", "from", "to"},
		Defaults: map[string]any{
			"smtp_server": "smtp.gmail.com",
			"smtp_port":   587,
		},
		Examples: []map[string]any{
			{
				"username":    "<EMAIL>",
				"password":    "app-password",
				"smtp_server": "smtp.gmail.com",
				"smtp_port":   587,
				"from":        "<EMAIL>",
				"to":          []string{"<EMAIL>", "<EMAIL>"},
			},
		},
	}
}

// SendAlert 发送告警
func (p *EmailAlerterPlugin) SendAlert(ctx context.Context, alert *plugininterface.AlertEvent) error {
	if len(p.to) == 0 {
		return fmt.Errorf("no recipients configured")
	}

	subject := fmt.Sprintf("[DevInsight Alert] %s - %s", alert.Level, alert.Title)
	body := p.buildEmailBody(alert)

	// 构建邮件内容
	message := fmt.Sprintf("To: %s\r\nSubject: %s\r\n\r\n%s",
		p.to[0], subject, body)

	// 发送邮件
	addr := fmt.Sprintf("%s:%d", p.smtpServer, p.smtpPort)
	auth := smtp.PlainAuth("", p.username, p.password, p.smtpServer)

	err := smtp.SendMail(addr, auth, p.from, p.to, []byte(message))
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	log.Printf("Alert email sent successfully to %v", p.to)
	return nil
}

// buildEmailBody 构建邮件正文
func (p *EmailAlerterPlugin) buildEmailBody(alert *plugininterface.AlertEvent) string {
	return fmt.Sprintf(`
告警详情：
===================
标题: %s
级别: %s
设备: %s (%s)
指标: %s
当前值: %v
阈值: %v
描述: %s
时间: %s

此邮件由 DevInsight 监控平台自动发送。
`,
		alert.Title,
		alert.Level,
		alert.DeviceName,
		alert.DeviceID,
		alert.MetricKey,
		alert.CurrentValue,
		alert.ThresholdValue,
		alert.Description,
		alert.Timestamp.Format("2006-01-02 15:04:05"),
	)
}

// ValidateConfig 验证配置
func (p *EmailAlerterPlugin) ValidateConfig(config map[string]interface{}) error {
	if _, ok := config["username"].(string); !ok {
		return fmt.Errorf("email username is required")
	}

	if _, ok := config["password"].(string); !ok {
		return fmt.Errorf("email password is required")
	}

	if to, ok := config["to"].([]string); !ok || len(to) == 0 {
		return fmt.Errorf("at least one recipient email is required")
	}

	return nil
}

// EmailPluginFactory 邮件插件工厂
type EmailPluginFactory struct{}

// CreatePlugin 创建插件实例
func (f *EmailPluginFactory) CreatePlugin(pluginType plugininterface.PluginType, config map[string]interface{}) (plugininterface.Plugin, error) {
	if pluginType != plugininterface.AlerterPlugin {
		return nil, fmt.Errorf("unsupported plugin type: %s", pluginType)
	}

	plugin := &EmailAlerterPlugin{
		info: &plugininterface.PluginInfo{
			Name:        "email-alerter",
			Version:     "1.0.0",
			Type:        plugininterface.AlerterPlugin,
			Description: "Email alerter plugin for sending alerts via SMTP",
			Author:      "DevInsight Team",
			Homepage:    "https://github.com/devinsight/email-alerter",
			License:     "MIT",
			Tags:        []string{"email", "smtp", "alerter", "notification"},
			CreatedAt:   time.Now(),
		},
	}

	return plugin, nil
}

// CheckCompatibility 检查兼容性
func (f *EmailPluginFactory) CheckCompatibility(systemVersion string) error {
	// Email alerter is compatible with all versions
	// It only uses standard Go SMTP library
	return nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *EmailPluginFactory) GetSupportedTypes() []plugininterface.PluginType {
	return []plugininterface.PluginType{plugininterface.AlerterPlugin}
}

// GetPluginInfo 导出函数 - 插件加载器需要
func GetPluginInfo() *plugininterface.PluginInfo {
	return &plugininterface.PluginInfo{
		Name:        "email-alerter",
		Version:     "1.0.0",
		Type:        plugininterface.AlerterPlugin,
		Description: "Email alerter plugin for sending alerts via SMTP",
		Author:      "DevInsight Team",
		Homepage:    "https://github.com/devinsight/email-alerter",
		License:     "MIT",
		Tags:        []string{"email", "smtp", "alerter", "notification"},
		CreatedAt:   time.Now(),
	}
}

// CreatePluginFactory 导出函数 - 插件加载器需要
func CreatePluginFactory() plugininterface.PluginFactory {
	return &EmailPluginFactory{}
}

func main() {
	// 这个文件会被编译为动态库(.so文件)
	// 不需要main函数，但为了编译通过而保留
}
