package main

import (
	"context"
	"fmt"
	"time"

	pb "aiops/pkg/proto"
	plugininterface "aiops/plugins/interface"
)

// MySQLCollectorPlugin MySQL 采集器插件示例
type MySQLCollectorPlugin struct {
	info   *plugininterface.PluginInfo
	config map[string]interface{}
}

// GetInfo 获取插件信息
func (p *MySQLCollectorPlugin) GetInfo() *plugininterface.PluginInfo {
	return p.info
}

// Initialize 初始化插件
func (p *MySQLCollectorPlugin) Initialize(ctx context.Context, config map[string]interface{}) error {
	p.config = config
	return nil
}

// Start 启动插件
func (p *MySQLCollectorPlugin) Start(ctx context.Context) error {
	fmt.Printf("MySQL Collector Plugin started\n")
	return nil
}

// Stop 停止插件
func (p *MySQLCollectorPlugin) Stop(ctx context.Context) error {
	fmt.Printf("MySQL Collector Plugin stopped\n")
	return nil
}

// Health 健康检查
func (p *MySQLCollectorPlugin) Health(ctx context.Context) error {
	return nil
}

// GetMetrics 获取插件指标
func (p *MySQLCollectorPlugin) GetMetrics(ctx context.Context) (map[string]interface{}, error) {
	return map[string]any{
		"connections_count":  42,
		"queries_per_second": 150.5,
		"uptime_seconds":     86400,
	}, nil
}

// GetSupportedDeviceTypes 获取支持的设备类型
func (p *MySQLCollectorPlugin) GetSupportedDeviceTypes() []string {
	return []string{"mysql", "mariadb"}
}

// GetSupportedMetrics 获取支持的指标
func (p *MySQLCollectorPlugin) GetSupportedMetrics(deviceType string) ([]*pb.SupportedMetric, error) {
	metrics := []*pb.SupportedMetric{
		{
			MetricKey:     "mysql.connections.active",
			MetricName:    "Active Connections",
			Description:   "Number of active MySQL connections",
			DataType:      "numeric",
			Unit:          "count",
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.queries.per_second",
			MetricName:    "Queries Per Second",
			Description:   "Number of queries executed per second",
			DataType:      "numeric",
			Unit:          "qps",
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.innodb.buffer_pool_hit_ratio",
			MetricName:    "InnoDB Buffer Pool Hit Ratio",
			Description:   "InnoDB buffer pool hit ratio percentage",
			DataType:      "numeric",
			Unit:          "percent",
			IsActive:      true,
			CollectorType: "mysql",
		},
	}

	return metrics, nil
}

// StartCollection 开始采集
func (p *MySQLCollectorPlugin) StartCollection(ctx context.Context, taskConfig *pb.CollectorTaskConfig) error {
	fmt.Printf("Started collection for task %s on device %s\n", taskConfig.TaskId, taskConfig.DeviceName)
	return nil
}

// StopCollection 停止采集
func (p *MySQLCollectorPlugin) StopCollection(ctx context.Context, taskID string) error {
	fmt.Printf("Stopped collection for task %s\n", taskID)
	return nil
}

// CollectMetrics 采集指标数据
func (p *MySQLCollectorPlugin) CollectMetrics(ctx context.Context, taskConfig *pb.CollectorTaskConfig) ([]*pb.MetricData, error) {
	now := time.Now().Unix()

	metrics := []*pb.MetricData{
		{
			DeviceId:  taskConfig.DeviceId,
			MetricKey: "mysql.connections.active",
			Timestamp: now,
			ValueType: &pb.MetricData_NumericValue{NumericValue: 45.0},
			Labels: map[string]string{
				"host": taskConfig.Host,
				"port": fmt.Sprintf("%d", taskConfig.Port),
			},
		},
		{
			DeviceId:  taskConfig.DeviceId,
			MetricKey: "mysql.queries.per_second",
			Timestamp: now,
			ValueType: &pb.MetricData_NumericValue{NumericValue: 156.7},
			Labels: map[string]string{
				"host": taskConfig.Host,
				"port": fmt.Sprintf("%d", taskConfig.Port),
			},
		},
		{
			DeviceId:  taskConfig.DeviceId,
			MetricKey: "mysql.innodb.buffer_pool_hit_ratio",
			Timestamp: now,
			ValueType: &pb.MetricData_NumericValue{NumericValue: 99.2},
			Labels: map[string]string{
				"host": taskConfig.Host,
				"port": fmt.Sprintf("%d", taskConfig.Port),
			},
		},
	}

	return metrics, nil
}

// ValidateConfig 验证配置
func (p *MySQLCollectorPlugin) ValidateConfig(config *pb.CollectorTaskConfig) error {
	if config.Host == "" {
		return fmt.Errorf("MySQL host is required")
	}

	if config.Port <= 0 {
		return fmt.Errorf("MySQL port must be positive")
	}

	if config.Username == "" {
		return fmt.Errorf("MySQL username is required")
	}

	return nil
}

// GetConfigSchema 获取配置模式
func (p *MySQLCollectorPlugin) GetConfigSchema() *plugininterface.ConfigSchema {
	return &plugininterface.ConfigSchema{
		Schema: map[string]any{
			"type": "object",
			"properties": map[string]any{
				"host": map[string]any{
					"type":        "string",
					"description": "MySQL server host",
					"default":     "localhost",
				},
				"port": map[string]any{
					"type":        "integer",
					"description": "MySQL server port",
					"default":     3306,
					"minimum":     1,
					"maximum":     65535,
				},
				"username": map[string]any{
					"type":        "string",
					"description": "MySQL username",
					"default":     "root",
				},
				"password": map[string]any{
					"type":        "string",
					"description": "MySQL password",
				},
				"database": map[string]any{
					"type":        "string",
					"description": "MySQL database name",
				},
				"collection_interval": map[string]any{
					"type":        "integer",
					"description": "Collection interval in seconds",
					"default":     60,
					"minimum":     1,
				},
			},
			"required": []string{"host", "username"},
		},
		Required: []string{"host", "username"},
		Defaults: map[string]any{
			"host":                "localhost",
			"port":                3306,
			"username":            "root",
			"collection_interval": 60,
		},
		Examples: []map[string]any{
			{
				"host":                "localhost",
				"port":                3306,
				"username":            "monitoring",
				"password":            "monitor123",
				"database":            "performance_schema",
				"collection_interval": 30,
			},
		},
	}
}

// MySQLPluginFactory MySQL 插件工厂
type MySQLPluginFactory struct{}

// CreatePlugin 创建插件实例
func (f *MySQLPluginFactory) CreatePlugin(pluginType plugininterface.PluginType, config map[string]interface{}) (plugininterface.Plugin, error) {
	if pluginType != plugininterface.CollectorPlugin {
		return nil, fmt.Errorf("unsupported plugin type: %s", pluginType)
	}

	plugin := &MySQLCollectorPlugin{
		info: &plugininterface.PluginInfo{
			Name:        "mysql-collector",
			Version:     "1.0.0",
			Type:        plugininterface.CollectorPlugin,
			Description: "MySQL database metrics collector",
			Author:      "DevInsight Team",
			Homepage:    "https://github.com/devinsight/mysql-collector",
			License:     "MIT",
			Tags:        []string{"mysql", "database", "collector"},
			CreatedAt:   time.Now(),
		},
	}

	return plugin, nil
}

// CheckCompatibility 检查兼容性
func (f *MySQLPluginFactory) CheckCompatibility(systemVersion string) error {
	// 简单的版本兼容性检查
	// 这里可以根据实际需求实现更复杂的检查逻辑
	return nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *MySQLPluginFactory) GetSupportedTypes() []plugininterface.PluginType {
	return []plugininterface.PluginType{plugininterface.CollectorPlugin}
}

// GetPluginInfo 导出函数 - 插件加载器需要
func GetPluginInfo() *plugininterface.PluginInfo {
	return &plugininterface.PluginInfo{
		Name:        "mysql-collector",
		Version:     "1.0.0",
		Type:        plugininterface.CollectorPlugin,
		Description: "MySQL database metrics collector",
		Author:      "DevInsight Team",
		Homepage:    "https://github.com/devinsight/mysql-collector",
		License:     "MIT",
		Tags:        []string{"mysql", "database", "collector"},
		CreatedAt:   time.Now(),
	}
}

// CreatePluginFactory 导出函数 - 插件加载器需要
func CreatePluginFactory() plugininterface.PluginFactory {
	return &MySQLPluginFactory{}
}

func main() {
	// 这个文件会被编译为动态库(.so文件)
	// 不需要main函数，但为了编译通过而保留
}
