#!/bin/bash

# 测试改进后的异常检测功能
set -e

BASE_URL="http://localhost:8080"
TOKEN="dev-token"

echo "=== 测试改进后的异常检测功能 ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

DEVICE_ID="improved-anomaly-test"
BASE_TIME=1732545000

echo "=============================================="
log_info "1. 检查插件配置"
echo "=============================================="

# 检查新的插件配置
response=$(curl -s "${BASE_URL}/api/plugins/simple-analyzer/1.0.0/metrics?token=${TOKEN}")
echo "Simple Analyzer新配置:"
echo "$response" | jq
threshold=$(echo "$response" | jq '.data.threshold')
log_info "当前默认阈值: $threshold"

echo
echo "=============================================="
log_info "2. 快速累积数据并测试"
echo "=============================================="

# 快速累积10批数据
for i in {1..10}; do
    START_TIME=$((BASE_TIME + i * 120))
    END_TIME=$((START_TIME + 120))
    
    curl -s -X POST "${BASE_URL}/api/plugins/analyze?token=${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"device_id\": \"${DEVICE_ID}\",
            \"start_time\": ${START_TIME},
            \"end_time\": ${END_TIME},
            \"limit\": 15
        }" > /dev/null
done

log_success "基础数据累积完成"

echo
echo "=============================================="
log_info "3. 使用默认阈值测试异常检测"
echo "=============================================="

# 使用默认阈值（现在应该是1.5）
response=$(curl -s -X POST "${BASE_URL}/api/plugins/detect-anomalies?token=${TOKEN}" \
    -H "Content-Type: application/json" \
    -d "{
        \"device_id\": \"${DEVICE_ID}\",
        \"start_time\": $((BASE_TIME + 500)),
        \"end_time\": $((BASE_TIME + 1500)),
        \"threshold\": ${threshold}
    }")

echo "默认阈值异常检测结果:"
echo "$response" | jq
anomaly_count=$(echo "$response" | jq '.data.anomaly_count // 0')
log_info "使用默认阈值检测到的异常数量: $anomaly_count"

echo
echo "=============================================="
log_info "4. 测试多种阈值"
echo "=============================================="

thresholds=(0.8 1.0 1.2 1.5 2.0 2.5)

for test_threshold in "${thresholds[@]}"; do
    log_info "测试阈值: $test_threshold"
    
    response=$(curl -s -X POST "${BASE_URL}/api/plugins/detect-anomalies?token=${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"device_id\": \"${DEVICE_ID}\",
            \"start_time\": $((BASE_TIME + 600)),
            \"end_time\": $((BASE_TIME + 1800)),
            \"threshold\": ${test_threshold}
        }")
    
    count=$(echo "$response" | jq '.data.anomaly_count // 0')
    if [[ "$count" -gt 0 ]]; then
        log_success "阈值 $test_threshold: 检测到 $count 个异常"
        # 显示第一个异常的详细信息
        echo "$response" | jq '.data.anomalies[0] | {device_id, metric_key, actual_value, expected_value, deviation_score, severity}'
    else
        log_warning "阈值 $test_threshold: 未检测到异常"
    fi
    echo
done

echo
echo "=============================================="
log_info "5. 验证各种指标类型的异常检测"
echo "=============================================="

# 为多个设备和指标类型生成数据
metrics_devices=("cpu-heavy-device" "memory-intensive-device" "network-busy-device")

for device in "${metrics_devices[@]}"; do
    log_info "为设备 $device 累积数据..."
    
    # 为每个设备累积足够的数据
    for j in {1..8}; do
        START_TIME=$((BASE_TIME + 200 + j * 150))
        END_TIME=$((START_TIME + 150))
        
        curl -s -X POST "${BASE_URL}/api/plugins/analyze?token=${TOKEN}" \
            -H "Content-Type: application/json" \
            -d "{
                \"device_id\": \"${device}\",
                \"start_time\": ${START_TIME},
                \"end_time\": ${END_TIME},
                \"limit\": 20
            }" > /dev/null
    done
    
    # 检测异常
    response=$(curl -s -X POST "${BASE_URL}/api/plugins/detect-anomalies?token=${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"device_id\": \"${device}\",
            \"start_time\": $((BASE_TIME + 800)),
            \"end_time\": $((BASE_TIME + 2000)),
            \"threshold\": 1.5
        }")
    
    count=$(echo "$response" | jq '.data.anomaly_count // 0')
    log_info "设备 $device 检测到 $count 个异常"
    
    if [[ "$count" -gt 0 ]]; then
        echo "异常详情:"
        echo "$response" | jq '.data.anomalies[] | {metric_key, deviation_score, severity}'
        echo
    fi
done

echo
echo "=============================================="
log_info "6. 总结测试结果"
echo "=============================================="

# 获取系统总体状态
plugins_response=$(curl -s "${BASE_URL}/api/plugins?token=${TOKEN}")
analyzer_count=$(echo "$plugins_response" | jq '.data.plugins | [.[] | select(.type == "analyzer")] | length')

health_response=$(curl -s -X POST "${BASE_URL}/api/plugins/health-check?token=${TOKEN}")
healthy_plugins=$(echo "$health_response" | jq '.data | length')

echo "系统状态总结:"
echo "- 分析器插件数量: $analyzer_count"
echo "- 健康插件数量: $healthy_plugins"
echo "- 异常检测改进: 阈值从 2.0 降低到 1.5"
echo "- 最小数据点要求: 从 10 降低到 5"
echo

if [[ "$anomaly_count" -gt 0 ]]; then
    log_success "异常检测改进成功！"
    echo "✓ 使用更敏感的默认阈值 (1.5)"
    echo "✓ 降低了最小数据点要求"
    echo "✓ 能够检测到实际异常"
else
    log_warning "建议进一步调整配置"
    echo "考虑继续降低默认阈值到 1.0"
fi

echo
log_info "异常检测改进测试完成"
