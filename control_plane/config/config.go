package config

import (
	"log"
	"os"
	"strconv"

	"github.com/spf13/viper"
)

// PluginConfig 插件系统配置
type PluginConfig struct {
	Enabled    bool     `yaml:"enabled"`
	PluginDirs []string `yaml:"plugin_dirs"`
	AutoLoad   bool     `yaml:"auto_load"`
	WatchMode  bool     `yaml:"watch_mode"`
}

// PluginDefaults 插件默认配置
type PluginDefaults struct {
	Collector struct {
		Timeout            string `yaml:"timeout"`
		RetryCount         int    `yaml:"retry_count"`
		RetryInterval      string `yaml:"retry_interval"`
		MaxConcurrentTasks int    `yaml:"max_concurrent_tasks"`
	} `yaml:"collector"`

	Processor struct {
		BatchSize     int    `yaml:"batch_size"`
		FlushInterval string `yaml:"flush_interval"`
		MaxQueueSize  int    `yaml:"max_queue_size"`
	} `yaml:"processor"`

	Alerter struct {
		Timeout    string `yaml:"timeout"`
		RetryCount int    `yaml:"retry_count"`
		RateLimit  int    `yaml:"rate_limit"`
	} `yaml:"alerter"`

	Analyzer struct {
		WindowSize          int     `yaml:"window_size"`
		ConfidenceThreshold float64 `yaml:"confidence_threshold"`
		AnomalyThreshold    float64 `yaml:"anomaly_threshold"`
	} `yaml:"analyzer"`
}

// Config 结构体定义了控制平面的配置项
type Config struct {
	HTTPPort       int     // HTTP API 服务端口
	GRPCPort       int     // gRPC 服务端口
	DatabasePath   string  // SQLite 数据库文件路径
	EmailSender    string  // 发送告警邮件的邮箱
	EmailPassword  string  // 邮箱密码
	EmailSMTPHost  string  // SMTP 服务器地址
	EmailSMTPPort  int     // SMTP 服务器端口
	AlertThreshold float64 // 告警阈值

	PluginConfig   `mapstructure:",squash"` // 嵌入式插件配置
	PluginDefaults `mapstructure:",squash"` // 嵌入式插件默认配置
}

// LoadConfig 从环境变量加载配置，如果环境变量不存在则使用默认值
func LoadConfig(v *viper.Viper) *Config {
	dsn := v.GetString("data.db.user.dsn")
	cfg := &Config{
		HTTPPort:       8080,  // 默认 HTTP 端口
		GRPCPort:       50051, // 默认 gRPC 端口
		DatabasePath:   dsn,
		EmailSMTPPort:  587,
		AlertThreshold: 90.0,
		PluginConfig: PluginConfig{
			Enabled:    true,
			PluginDirs: []string{"./plugins"},
			AutoLoad:   true,
			WatchMode:  false,
		},
		PluginDefaults: PluginDefaults{
			Collector: struct {
				Timeout            string `yaml:"timeout"`
				RetryCount         int    `yaml:"retry_count"`
				RetryInterval      string `yaml:"retry_interval"`
				MaxConcurrentTasks int    `yaml:"max_concurrent_tasks"`
			}{
				Timeout:            "5s",
				RetryCount:         3,
				RetryInterval:      "2s",
				MaxConcurrentTasks: 10,
			},
			Processor: struct {
				BatchSize     int    `yaml:"batch_size"`
				FlushInterval string `yaml:"flush_interval"`
				MaxQueueSize  int    `yaml:"max_queue_size"`
			}{
				BatchSize:     100,
				FlushInterval: "1s",
				MaxQueueSize:  1000,
			},
			Alerter: struct {
				Timeout    string `yaml:"timeout"`
				RetryCount int    `yaml:"retry_count"`
				RateLimit  int    `yaml:"rate_limit"`
			}{
				Timeout:    "5s",
				RetryCount: 3,
				RateLimit:  10,
			},
			Analyzer: struct {
				WindowSize          int     `yaml:"window_size"`
				ConfidenceThreshold float64 `yaml:"confidence_threshold"`
				AnomalyThreshold    float64 `yaml:"anomaly_threshold"`
			}{
				WindowSize:          60,
				ConfidenceThreshold: 0.95,
				AnomalyThreshold:    1.5,
			},
		},
	}

	// 从环境变量加载配置
	if port := os.Getenv("DEVINSIGHT_HTTP_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			cfg.HTTPPort = p
		} else {
			log.Printf("警告: HTTP端口解析失败，使用默认值 %d: %v", cfg.HTTPPort, err)
		}
	}

	if port := os.Getenv("DEVINSIGHT_GRPC_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			cfg.GRPCPort = p
		} else {
			log.Printf("警告: gRPC端口解析失败，使用默认值 %d: %v", cfg.GRPCPort, err)
		}
	}

	if dbPath := os.Getenv("DEVINSIGHT_DB_PATH"); dbPath != "" {
		cfg.DatabasePath = dbPath
	}

	// 邮件配置
	cfg.EmailSender = os.Getenv("DEVINSIGHT_EMAIL_SENDER")
	cfg.EmailPassword = os.Getenv("DEVINSIGHT_EMAIL_PASSWORD")
	cfg.EmailSMTPHost = os.Getenv("DEVINSIGHT_EMAIL_SMTP_HOST")

	if port := os.Getenv("DEVINSIGHT_EMAIL_SMTP_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			cfg.EmailSMTPPort = p
		}
	}

	if threshold := os.Getenv("DEVINSIGHT_ALERT_THRESHOLD"); threshold != "" {
		if t, err := strconv.ParseFloat(threshold, 64); err == nil {
			cfg.AlertThreshold = t
		}
	}

	// 打印加载的配置信息（敏感信息隐藏）
	log.Printf("配置加载完成: HTTP端口=%d, gRPC端口=%d, 数据库路径=%s, SMTP服务器=%s:%d, 告警阈值=%.2f",
		cfg.HTTPPort, cfg.GRPCPort, cfg.DatabasePath, cfg.EmailSMTPHost, cfg.EmailSMTPPort, cfg.AlertThreshold)

	return cfg
}

// LoadPluginConfig 从 plugins.yaml 加载插件配置
func LoadPluginConfig() (*PluginConfig, error) {
	v := viper.New()
	v.SetConfigName("plugins")
	v.SetConfigType("yaml")
	v.AddConfigPath("./config")
	v.AddConfigPath("../config")
	v.AddConfigPath(".")

	if err := v.ReadInConfig(); err != nil {
		log.Printf("警告: 无法读取插件配置文件: %v, 使用默认配置", err)
		return &PluginConfig{
			Enabled: true,
			PluginDirs: []string{
				"./plugins/build",
				"./plugins/examples",
			},
			AutoLoad:  true,
			WatchMode: false,
		}, nil
	}

	// 解析插件配置
	var config struct {
		Plugin PluginConfig `yaml:"plugin"`
	}

	if err := v.Unmarshal(&config); err != nil {
		return nil, err
	}

	// 如果没有配置插件目录，使用默认值
	if len(config.Plugin.PluginDirs) == 0 {
		config.Plugin.PluginDirs = []string{
			"./plugins/build",
			"./plugins/examples",
		}
	}

	log.Printf("插件配置加载完成: 启用=%v, 目录=%v, 自动加载=%v",
		config.Plugin.Enabled, config.Plugin.PluginDirs, config.Plugin.AutoLoad)

	return &config.Plugin, nil
}
