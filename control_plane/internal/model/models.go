package model

import (
	"time"

	"gorm.io/gorm"
)

// Agent 采集代理模型
type Agent struct {
	gorm.Model
	AgentID                 string `gorm:"uniqueIndex"`
	AgentIP                 string
	SupportedCollectorTypes string `gorm:"type:text"` // JSON 字符串
	Status                  string // online, offline
	LastHeartbeat           time.Time
	DeviceConfig            string `gorm:"type:text"` // JSON格式的设备配置信息
}

// Device 设备模型
type Device struct {
	gorm.Model
	Name            string `gorm:"uniqueIndex"`
	Type            string // mysql, redis 等
	Host            string
	Port            int
	Username        string
	Password        string
	ConnectParams   string `gorm:"type:text"` // JSON 字符串
	AgentID         string `gorm:"index"`
	Status          string // online, offline, unknown
	LastCollectTime time.Time
}

// CollectorTask 采集任务模型
type CollectorTask struct {
	gorm.Model
	TaskID          string        `gorm:"uniqueIndex"`
	DeviceID        uint          `gorm:"index"`
	AgentID         string        `gorm:"index"`
	TaskName        string        `gorm:"uniqueIndex"`
	Frequency       time.Duration // 采集频率（秒）
	CollectorType   string        // 采集器类型
	CollectItems    string        `gorm:"type:text"` // JSON 字符串
	IsEnabled       bool
	Status          string // running, stopped, error
	LastError       string
	LastCollectTime time.Time
}

// MetricData 指标数据模型 - 支持灵活的多维数据存储
type MetricData struct {
	gorm.Model
	DeviceID     string   `gorm:"index"`
	MetricKey    string   `gorm:"index"`
	NumericValue *float64 // 数值类型的值，可为空
	StringValue  *string  // 字符串类型的值，可为空
	BooleanValue *bool    // 布尔类型的值，可为空
	JSONData     string   `gorm:"type:text"` // 灵活的JSON格式数据，支持复杂的多维指标
	Timestamp    int64    `gorm:"index"`
	Labels       string   `gorm:"type:text"` // JSON 字符串
}

// AlertRule 告警规则模型
type AlertRule struct {
	gorm.Model
	RuleName             string `gorm:"uniqueIndex"`
	DeviceID             uint   `gorm:"index"`
	MetricKey            string `gorm:"index"`
	RuleType             string // threshold, trend
	ThresholdOp          string // >, <, ==, >= 等
	ThresholdValue       float64
	Duration             time.Duration // 持续时间
	NotificationChannels string        `gorm:"type:text"` // JSON 字符串
	MessageTemplate      string
	IsEnabled            bool
}

// AlertEvent 告警事件模型
type AlertEvent struct {
	gorm.Model
	RuleID       uint   `gorm:"index"`
	DeviceID     uint   `gorm:"index"`
	MetricKey    string `gorm:"index"`
	Severity     string // critical, warning, info
	Message      string
	TriggerValue float64
	TriggerTime  time.Time
	Status       string // firing, resolved
	ResolvedTime *time.Time
}

// User 用户模型
type User struct {
	gorm.Model
	Username     string `gorm:"uniqueIndex"`
	PasswordHash string
	Role         string // admin, operator, viewer
}

// TaskStatus 任务状态结构
type TaskStatus struct {
	TaskID               string
	Status               string // running, stopped, error, success
	ErrorMessage         string
	LastCollectTimestamp time.Time
}

// SupportedMetric 支持的指标定义模型
type SupportedMetric struct {
	gorm.Model
	MetricKey     string `gorm:"uniqueIndex"`
	MetricName    string
	Description   string `gorm:"type:text"`
	DataType      string // numeric, string, boolean, json
	Unit          string
	Metadata      string `gorm:"type:text"` // JSON格式的元数据
	IsActive      bool
	CollectorType string `gorm:"index"`
}

// LogEntry 日志条目模型
type LogEntry struct {
	gorm.Model
	DeviceID  string `gorm:"index"`
	LogLevel  string // DEBUG, INFO, WARN, ERROR
	Message   string `gorm:"type:text"`
	Timestamp int64  `gorm:"index"`
	Source    string // 日志来源：agent, collector, etc.
	Fields    string `gorm:"type:text"` // JSON格式的额外结构化字段
}

// DeviceConfig Agent设备配置信息结构
type DeviceConfig struct {
	MaxMemoryMB        int64             `json:"max_memory_mb"`
	MaxCPUPercent      int32             `json:"max_cpu_percent"`
	MaxDiskMB          int64             `json:"max_disk_mb"`
	MaxConcurrentTasks int32             `json:"max_concurrent_tasks"`
	Capabilities       map[string]string `json:"capabilities"`
}

// CollectorTaskConfig 任务配置结构，用于与Agent通信
type CollectorTaskConfig struct {
	TaskID        string
	DeviceID      string
	DeviceName    string
	DeviceType    string
	Host          string
	Port          int
	Username      string
	Password      string
	ConnectParams string // JSON 格式的连接参数
	Frequency     time.Duration
	CollectItems  string // JSON 格式的采集项列表
	IsEnabled     bool
}
