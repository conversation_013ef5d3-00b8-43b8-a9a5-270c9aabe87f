package repository

import (
	"fmt"
	"time"

	"aiops/control_plane/internal/model"

	"gorm.io/gorm"
)

// LogRepository 接口定义了日志条目的存储操作
type LogRepository interface {
	SaveLogs(logs []*model.LogEntry) error
	GetLogsByDeviceID(deviceID string, startTime, endTime time.Time, logLevel string, limit int) ([]*model.LogEntry, error)
	GetLogsByTimeRange(startTime, endTime time.Time, logLevel string, limit int) ([]*model.LogEntry, error)
	GetLogsBySource(source string, startTime, endTime time.Time, limit int) ([]*model.LogEntry, error)
	DeleteLogsBeforeTime(time time.Time) error
}

// logRepositoryImpl 是 LogRepository 接口的实现
type logRepositoryImpl struct {
	db *gorm.DB
}

// NewLogRepository 创建一个新的 LogRepository 实例
func NewLogRepository(db *gorm.DB) LogRepository {
	return &logRepositoryImpl{db: db}
}

// SaveLogs 批量保存日志数据
func (r *logRepositoryImpl) SaveLogs(logs []*model.LogEntry) error {
	if len(logs) == 0 {
		return nil
	}

	result := r.db.CreateInBatches(logs, 100) // 分批处理，每批 100 条
	if result.Error != nil {
		return fmt.Errorf("保存日志数据失败: %w", result.Error)
	}

	return nil
}

// GetLogsByDeviceID 根据设备ID获取日志
func (r *logRepositoryImpl) GetLogsByDeviceID(deviceID string, startTime, endTime time.Time, logLevel string, limit int) ([]*model.LogEntry, error) {
	var logs []*model.LogEntry
	query := r.db.Where("device_id = ? AND timestamp >= ? AND timestamp <= ?", deviceID, startTime.Unix(), endTime.Unix())

	if logLevel != "" {
		query = query.Where("log_level = ?", logLevel)
	}

	result := query.Order("timestamp DESC").Limit(limit).Find(&logs)
	if result.Error != nil {
		return nil, fmt.Errorf("根据设备ID查询日志失败: %w", result.Error)
	}

	return logs, nil
}

// GetLogsByTimeRange 根据时间范围获取日志
func (r *logRepositoryImpl) GetLogsByTimeRange(startTime, endTime time.Time, logLevel string, limit int) ([]*model.LogEntry, error) {
	var logs []*model.LogEntry
	query := r.db.Where("timestamp >= ? AND timestamp <= ?", startTime.Unix(), endTime.Unix())

	if logLevel != "" {
		query = query.Where("log_level = ?", logLevel)
	}

	result := query.Order("timestamp DESC").Limit(limit).Find(&logs)
	if result.Error != nil {
		return nil, fmt.Errorf("根据时间范围查询日志失败: %w", result.Error)
	}

	return logs, nil
}

// GetLogsBySource 根据日志来源获取日志
func (r *logRepositoryImpl) GetLogsBySource(source string, startTime, endTime time.Time, limit int) ([]*model.LogEntry, error) {
	var logs []*model.LogEntry
	result := r.db.Where("source = ? AND timestamp >= ? AND timestamp <= ?", source, startTime.Unix(), endTime.Unix()).
		Order("timestamp DESC").Limit(limit).Find(&logs)
	if result.Error != nil {
		return nil, fmt.Errorf("根据来源查询日志失败: %w", result.Error)
	}

	return logs, nil
}

// DeleteLogsBeforeTime 删除指定时间之前的日志
func (r *logRepositoryImpl) DeleteLogsBeforeTime(time time.Time) error {
	result := r.db.Where("timestamp < ?", time.Unix()).Delete(&model.LogEntry{})
	if result.Error != nil {
		return fmt.Errorf("删除过期日志失败: %w", result.Error)
	}
	return nil
}
