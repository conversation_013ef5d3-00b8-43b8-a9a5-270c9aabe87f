package repository

import (
	"fmt"

	"aiops/control_plane/internal/model"

	"gorm.io/gorm"
)

// AgentRepository 接口定义了 Agent 数据的存储操作
type AgentRepository interface {
	CreateAgent(agent *model.Agent) error
	GetAgentByID(agentID string) (*model.Agent, error)
	UpdateAgent(agent *model.Agent) error
	DeleteAgent(agentID string) error
	GetAllAgents() ([]*model.Agent, error)
}

// agentRepositoryImpl 是 AgentRepository 接口的实现
type agentRepositoryImpl struct {
	db *gorm.DB
}

// NewAgentRepository 创建一个新的 AgentRepository 实例
func NewAgentRepository(db *gorm.DB) AgentRepository {
	return &agentRepositoryImpl{db: db}
}

// CreateAgent 创建一个新的 Agent 记录
func (r *agentRepositoryImpl) CreateAgent(agent *model.Agent) error {
	result := r.db.Create(agent)
	if result.Error != nil {
		return fmt.Errorf("创建 Agent 失败: %w", result.Error)
	}
	return nil
}

// GetAgentByID 根据 ID 获取 Agent
func (r *agentRepositoryImpl) GetAgentByID(agentID string) (*model.Agent, error) {
	var agent model.Agent
	result := r.db.Where("agent_id = ?", agentID).First(&agent)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil // 返回 nil, nil 表示未找到记录
		}
		return nil, fmt.Errorf("查询 Agent 失败: %w", result.Error)
	}
	return &agent, nil
}

// UpdateAgent 更新 Agent 信息
func (r *agentRepositoryImpl) UpdateAgent(agent *model.Agent) error {
	result := r.db.Save(agent)
	if result.Error != nil {
		return fmt.Errorf("更新 Agent 失败: %w", result.Error)
	}
	return nil
}

// DeleteAgent 删除 Agent
func (r *agentRepositoryImpl) DeleteAgent(agentID string) error {
	result := r.db.Where("agent_id = ?", agentID).Delete(&model.Agent{})
	if result.Error != nil {
		return fmt.Errorf("删除 Agent 失败: %w", result.Error)
	}
	return nil
}

// GetAllAgents 获取所有 Agent
func (r *agentRepositoryImpl) GetAllAgents() ([]*model.Agent, error) {
	var agents []*model.Agent
	result := r.db.Find(&agents)
	if result.Error != nil {
		return nil, fmt.Errorf("查询所有 Agent 失败: %w", result.Error)
	}
	return agents, nil
}
