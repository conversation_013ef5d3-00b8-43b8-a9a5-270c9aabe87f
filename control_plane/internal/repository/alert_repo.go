package repository

import (
	"fmt"
	"time"

	"aiops/control_plane/internal/model"

	"gorm.io/gorm"
)

// AlertRuleRepository 接口定义了告警规则的存储操作
type AlertRuleRepository interface {
	CreateAlertRule(rule *model.AlertRule) error
	GetAlertRuleByID(id uint) (*model.AlertRule, error)
	UpdateAlertRule(rule *model.AlertRule) error
	DeleteAlertRule(id uint) error
	GetAllAlertRules() ([]*model.AlertRule, error)
	GetAlertRulesByDeviceID(deviceID uint) ([]*model.AlertRule, error)
	GetEnabledAlertRules() ([]*model.AlertRule, error)
}

// alertRuleRepositoryImpl 是 AlertRuleRepository 接口的实现
type alertRuleRepositoryImpl struct {
	db *gorm.DB
}

// NewAlertRuleRepository 创建一个新的 AlertRuleRepository 实例
func NewAlertRuleRepository(db *gorm.DB) AlertRuleRepository {
	return &alertRuleRepositoryImpl{db: db}
}

// CreateAlertRule 创建告警规则
func (r *alertRuleRepositoryImpl) CreateAlertRule(rule *model.AlertRule) error {
	result := r.db.Create(rule)
	if result.Error != nil {
		return fmt.Errorf("创建告警规则失败: %w", result.Error)
	}
	return nil
}

// GetAlertRuleByID 根据 ID 获取告警规则
func (r *alertRuleRepositoryImpl) GetAlertRuleByID(id uint) (*model.AlertRule, error) {
	var rule model.AlertRule
	result := r.db.First(&rule, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询告警规则失败: %w", result.Error)
	}
	return &rule, nil
}

// UpdateAlertRule 更新告警规则
func (r *alertRuleRepositoryImpl) UpdateAlertRule(rule *model.AlertRule) error {
	result := r.db.Save(rule)
	if result.Error != nil {
		return fmt.Errorf("更新告警规则失败: %w", result.Error)
	}
	return nil
}

// DeleteAlertRule 删除告警规则
func (r *alertRuleRepositoryImpl) DeleteAlertRule(id uint) error {
	result := r.db.Delete(&model.AlertRule{}, id)
	if result.Error != nil {
		return fmt.Errorf("删除告警规则失败: %w", result.Error)
	}
	return nil
}

// GetAllAlertRules 获取所有告警规则
func (r *alertRuleRepositoryImpl) GetAllAlertRules() ([]*model.AlertRule, error) {
	var rules []*model.AlertRule
	result := r.db.Find(&rules)
	if result.Error != nil {
		return nil, fmt.Errorf("查询所有告警规则失败: %w", result.Error)
	}
	return rules, nil
}

// GetAlertRulesByDeviceID 根据设备 ID 获取告警规则
func (r *alertRuleRepositoryImpl) GetAlertRulesByDeviceID(deviceID uint) ([]*model.AlertRule, error) {
	var rules []*model.AlertRule
	result := r.db.Where("device_id = ?", deviceID).Find(&rules)
	if result.Error != nil {
		return nil, fmt.Errorf("根据设备 ID 查询告警规则失败: %w", result.Error)
	}
	return rules, nil
}

// GetEnabledAlertRules 获取所有启用的告警规则
func (r *alertRuleRepositoryImpl) GetEnabledAlertRules() ([]*model.AlertRule, error) {
	var rules []*model.AlertRule
	result := r.db.Where("is_enabled = ?", true).Find(&rules)
	if result.Error != nil {
		return nil, fmt.Errorf("查询启用的告警规则失败: %w", result.Error)
	}
	return rules, nil
}

// AlertEventRepository 接口定义了告警事件的存储操作
type AlertEventRepository interface {
	CreateAlertEvent(event *model.AlertEvent) error
	GetAlertEventByID(id uint) (*model.AlertEvent, error)
	UpdateAlertEvent(event *model.AlertEvent) error
	GetActiveAlertEvents() ([]*model.AlertEvent, error)
	GetAlertEventsByDeviceID(deviceID uint, limit int) ([]*model.AlertEvent, error)
	GetAlertEventsByRuleID(ruleID uint, limit int) ([]*model.AlertEvent, error)
	GetAlertEventsByTimeRange(startTime, endTime time.Time, limit int) ([]*model.AlertEvent, error)
}

// alertEventRepositoryImpl 是 AlertEventRepository 接口的实现
type alertEventRepositoryImpl struct {
	db *gorm.DB
}

// NewAlertEventRepository 创建一个新的 AlertEventRepository 实例
func NewAlertEventRepository(db *gorm.DB) AlertEventRepository {
	return &alertEventRepositoryImpl{db: db}
}

// CreateAlertEvent 创建告警事件
func (r *alertEventRepositoryImpl) CreateAlertEvent(event *model.AlertEvent) error {
	result := r.db.Create(event)
	if result.Error != nil {
		return fmt.Errorf("创建告警事件失败: %w", result.Error)
	}
	return nil
}

// GetAlertEventByID 根据 ID 获取告警事件
func (r *alertEventRepositoryImpl) GetAlertEventByID(id uint) (*model.AlertEvent, error) {
	var event model.AlertEvent
	result := r.db.First(&event, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询告警事件失败: %w", result.Error)
	}
	return &event, nil
}

// UpdateAlertEvent 更新告警事件
func (r *alertEventRepositoryImpl) UpdateAlertEvent(event *model.AlertEvent) error {
	result := r.db.Save(event)
	if result.Error != nil {
		return fmt.Errorf("更新告警事件失败: %w", result.Error)
	}
	return nil
}

// GetActiveAlertEvents 获取所有活跃的告警事件
func (r *alertEventRepositoryImpl) GetActiveAlertEvents() ([]*model.AlertEvent, error) {
	var events []*model.AlertEvent
	result := r.db.Where("status = ?", "firing").Find(&events)
	if result.Error != nil {
		return nil, fmt.Errorf("查询活跃告警事件失败: %w", result.Error)
	}
	return events, nil
}

// GetAlertEventsByDeviceID 根据设备 ID 获取告警事件
func (r *alertEventRepositoryImpl) GetAlertEventsByDeviceID(deviceID uint, limit int) ([]*model.AlertEvent, error) {
	var events []*model.AlertEvent

	query := r.db.Where("device_id = ?", deviceID).
		Order("trigger_time DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	result := query.Find(&events)
	if result.Error != nil {
		return nil, fmt.Errorf("根据设备 ID 查询告警事件失败: %w", result.Error)
	}

	return events, nil
}

// GetAlertEventsByRuleID 根据规则 ID 获取告警事件
func (r *alertEventRepositoryImpl) GetAlertEventsByRuleID(ruleID uint, limit int) ([]*model.AlertEvent, error) {
	var events []*model.AlertEvent

	query := r.db.Where("rule_id = ?", ruleID).
		Order("trigger_time DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	result := query.Find(&events)
	if result.Error != nil {
		return nil, fmt.Errorf("根据规则 ID 查询告警事件失败: %w", result.Error)
	}

	return events, nil
}

// GetAlertEventsByTimeRange 根据时间范围获取告警事件
func (r *alertEventRepositoryImpl) GetAlertEventsByTimeRange(startTime, endTime time.Time, limit int) ([]*model.AlertEvent, error) {
	var events []*model.AlertEvent

	query := r.db.Where("trigger_time >= ?", startTime).
		Where("trigger_time <= ?", endTime).
		Order("trigger_time DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	result := query.Find(&events)
	if result.Error != nil {
		return nil, fmt.Errorf("根据时间范围查询告警事件失败: %w", result.Error)
	}

	return events, nil
}
