package repository

import (
	"fmt"

	"aiops/control_plane/internal/model"

	"gorm.io/gorm"
)

// UserRepository 接口定义了用户数据的存储操作
type UserRepository interface {
	CreateUser(user *model.User) error
	GetUserByID(id uint) (*model.User, error)
	GetUserByUsername(username string) (*model.User, error)
	UpdateUser(user *model.User) error
	DeleteUser(id uint) error
	GetAllUsers() ([]*model.User, error)
}

// userRepositoryImpl 是 UserRepository 接口的实现
type userRepositoryImpl struct {
	db *gorm.DB
}

// NewUserRepository 创建一个新的 UserRepository 实例
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepositoryImpl{db: db}
}

// CreateUser 创建用户
func (r *userRepositoryImpl) CreateUser(user *model.User) error {
	result := r.db.Create(user)
	if result.Error != nil {
		return fmt.Errorf("创建用户失败: %w", result.Error)
	}
	return nil
}

// GetUserByID 根据 ID 获取用户
func (r *userRepositoryImpl) GetUserByID(id uint) (*model.User, error) {
	var user model.User
	result := r.db.First(&user, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询用户失败: %w", result.Error)
	}
	return &user, nil
}

// GetUserByUsername 根据用户名获取用户
func (r *userRepositoryImpl) GetUserByUsername(username string) (*model.User, error) {
	var user model.User
	result := r.db.Where("username = ?", username).First(&user)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("根据用户名查询用户失败: %w", result.Error)
	}
	return &user, nil
}

// UpdateUser 更新用户信息
func (r *userRepositoryImpl) UpdateUser(user *model.User) error {
	result := r.db.Save(user)
	if result.Error != nil {
		return fmt.Errorf("更新用户信息失败: %w", result.Error)
	}
	return nil
}

// DeleteUser 删除用户
func (r *userRepositoryImpl) DeleteUser(id uint) error {
	result := r.db.Delete(&model.User{}, id)
	if result.Error != nil {
		return fmt.Errorf("删除用户失败: %w", result.Error)
	}
	return nil
}

// GetAllUsers 获取所有用户
func (r *userRepositoryImpl) GetAllUsers() ([]*model.User, error) {
	var users []*model.User
	result := r.db.Find(&users)
	if result.Error != nil {
		return nil, fmt.Errorf("查询所有用户失败: %w", result.Error)
	}
	return users, nil
}
