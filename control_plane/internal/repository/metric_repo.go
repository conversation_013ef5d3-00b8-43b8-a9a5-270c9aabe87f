package repository

import (
	"fmt"
	"time"

	"aiops/control_plane/internal/model"

	"gorm.io/gorm"
)

// MetricRepository 接口定义了指标数据的存储操作
type MetricRepository interface {
	SaveMetrics(metrics []*model.MetricData) error
	GetMetricsByDeviceID(deviceID string, startTime, endTime time.Time, limit int) ([]*model.MetricData, error)
	GetMetricsByKey(deviceID, metricKey string, startTime, endTime time.Time, limit int) ([]*model.MetricData, error)
	GetLatestMetric(deviceID, metricKey string) (*model.MetricData, error)
	GetMetricsByDataType(deviceID, dataType string, startTime, endTime time.Time, limit int) ([]*model.MetricData, error)
	DeleteMetricsBeforeTime(time time.Time) error
}

// metricRepositoryImpl 是 MetricRepository 接口的实现
type metricRepositoryImpl struct {
	db *gorm.DB
}

// NewMetricRepository 创建一个新的 MetricRepository 实例
func NewMetricRepository(db *gorm.DB) MetricRepository {
	return &metricRepositoryImpl{db: db}
}

// SaveMetrics 批量保存指标数据
func (r *metricRepositoryImpl) SaveMetrics(metrics []*model.MetricData) error {
	if len(metrics) == 0 {
		return nil
	}

	result := r.db.CreateInBatches(metrics, 100) // 分批处理，每批 100 条
	if result.Error != nil {
		return fmt.Errorf("保存指标数据失败: %w", result.Error)
	}

	return nil
}

// GetMetricsByDeviceID 根据设备 ID 查询指标数据
func (r *metricRepositoryImpl) GetMetricsByDeviceID(deviceID string, startTime, endTime time.Time, limit int) ([]*model.MetricData, error) {
	var metrics []*model.MetricData

	query := r.db.Where("device_id = ?", deviceID).
		Where("timestamp >= ?", startTime.Unix()).
		Where("timestamp <= ?", endTime.Unix()).
		Order("timestamp DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	result := query.Find(&metrics)
	if result.Error != nil {
		return nil, fmt.Errorf("查询设备指标数据失败: %w", result.Error)
	}

	return metrics, nil
}

// GetMetricsByKey 根据指标键和设备 ID 查询指标数据
func (r *metricRepositoryImpl) GetMetricsByKey(deviceID, metricKey string, startTime, endTime time.Time, limit int) ([]*model.MetricData, error) {
	var metrics []*model.MetricData

	query := r.db.Where("device_id = ?", deviceID).
		Where("metric_key = ?", metricKey).
		Where("timestamp >= ?", startTime.Unix()).
		Where("timestamp <= ?", endTime.Unix()).
		Order("timestamp DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	result := query.Find(&metrics)
	if result.Error != nil {
		return nil, fmt.Errorf("查询指标数据失败: %w", result.Error)
	}

	return metrics, nil
}

// GetLatestMetric 获取最新的指标数据
func (r *metricRepositoryImpl) GetLatestMetric(deviceID, metricKey string) (*model.MetricData, error) {
	var metric model.MetricData

	result := r.db.Where("device_id = ?", deviceID).
		Where("metric_key = ?", metricKey).
		Order("timestamp DESC").
		First(&metric)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询最新指标数据失败: %w", result.Error)
	}

	return &metric, nil
}

// GetMetricsByDataType 根据数据类型获取指标数据
func (r *metricRepositoryImpl) GetMetricsByDataType(deviceID, dataType string, startTime, endTime time.Time, limit int) ([]*model.MetricData, error) {
	var metrics []*model.MetricData
	query := r.db.Where("device_id = ? AND timestamp >= ? AND timestamp <= ?", deviceID, startTime.Unix(), endTime.Unix())

	// 根据数据类型过滤
	switch dataType {
	case "numeric":
		query = query.Where("numeric_value IS NOT NULL")
	case "string":
		query = query.Where("string_value IS NOT NULL")
	case "boolean":
		query = query.Where("boolean_value IS NOT NULL")
	case "json":
		query = query.Where("json_data != '' AND json_data IS NOT NULL")
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	result := query.Order("timestamp DESC").Find(&metrics)
	if result.Error != nil {
		return nil, fmt.Errorf("根据数据类型查询指标数据失败: %w", result.Error)
	}

	return metrics, nil
}

// DeleteMetricsBeforeTime 删除指定时间之前的指标数据（用于清理历史数据）
func (r *metricRepositoryImpl) DeleteMetricsBeforeTime(t time.Time) error {
	result := r.db.Where("timestamp < ?", t.Unix()).Delete(&model.MetricData{})
	if result.Error != nil {
		return fmt.Errorf("删除历史指标数据失败: %w", result.Error)
	}

	return nil
}
