package repository

import (
	"fmt"

	"aiops/control_plane/internal/model"

	"gorm.io/gorm"
)

// SupportedMetricRepository 接口定义了支持的指标的存储操作
type SupportedMetricRepository interface {
	CreateSupportedMetric(metric *model.SupportedMetric) error
	GetSupportedMetricByKey(metricKey string) (*model.SupportedMetric, error)
	GetSupportedMetricByID(id uint) (*model.SupportedMetric, error)
	UpdateSupportedMetric(metric *model.SupportedMetric) error
	DeleteSupportedMetric(id uint) error
	GetAllSupportedMetrics() ([]*model.SupportedMetric, error)
	GetSupportedMetricsByCollectorType(collectorType string) ([]*model.SupportedMetric, error)
	GetActiveSupportedMetrics() ([]*model.SupportedMetric, error)
}

// supportedMetricRepositoryImpl 是 SupportedMetricRepository 接口的实现
type supportedMetricRepositoryImpl struct {
	db *gorm.DB
}

// NewSupportedMetricRepository 创建一个新的 SupportedMetricRepository 实例
func NewSupportedMetricRepository(db *gorm.DB) SupportedMetricRepository {
	return &supportedMetricRepositoryImpl{db: db}
}

// CreateSupportedMetric 创建新的支持指标
func (r *supportedMetricRepositoryImpl) CreateSupportedMetric(metric *model.SupportedMetric) error {
	result := r.db.Create(metric)
	if result.Error != nil {
		return fmt.Errorf("创建支持指标失败: %w", result.Error)
	}
	return nil
}

// GetSupportedMetricByKey 根据指标键获取支持指标
func (r *supportedMetricRepositoryImpl) GetSupportedMetricByKey(metricKey string) (*model.SupportedMetric, error) {
	var metric model.SupportedMetric
	result := r.db.Where("metric_key = ?", metricKey).First(&metric)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("根据指标键查询支持指标失败: %w", result.Error)
	}
	return &metric, nil
}

// GetSupportedMetricByID 根据ID获取支持指标
func (r *supportedMetricRepositoryImpl) GetSupportedMetricByID(id uint) (*model.SupportedMetric, error) {
	var metric model.SupportedMetric
	result := r.db.First(&metric, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("根据ID查询支持指标失败: %w", result.Error)
	}
	return &metric, nil
}

// UpdateSupportedMetric 更新支持指标
func (r *supportedMetricRepositoryImpl) UpdateSupportedMetric(metric *model.SupportedMetric) error {
	result := r.db.Save(metric)
	if result.Error != nil {
		return fmt.Errorf("更新支持指标失败: %w", result.Error)
	}
	return nil
}

// DeleteSupportedMetric 删除支持指标
func (r *supportedMetricRepositoryImpl) DeleteSupportedMetric(id uint) error {
	result := r.db.Delete(&model.SupportedMetric{}, id)
	if result.Error != nil {
		return fmt.Errorf("删除支持指标失败: %w", result.Error)
	}
	return nil
}

// GetAllSupportedMetrics 获取所有支持指标
func (r *supportedMetricRepositoryImpl) GetAllSupportedMetrics() ([]*model.SupportedMetric, error) {
	var metrics []*model.SupportedMetric
	result := r.db.Find(&metrics)
	if result.Error != nil {
		return nil, fmt.Errorf("查询所有支持指标失败: %w", result.Error)
	}
	return metrics, nil
}

// GetSupportedMetricsByCollectorType 根据采集器类型获取支持指标
func (r *supportedMetricRepositoryImpl) GetSupportedMetricsByCollectorType(collectorType string) ([]*model.SupportedMetric, error) {
	var metrics []*model.SupportedMetric
	result := r.db.Where("collector_type = ?", collectorType).Find(&metrics)
	if result.Error != nil {
		return nil, fmt.Errorf("根据采集器类型查询支持指标失败: %w", result.Error)
	}
	return metrics, nil
}

// GetActiveSupportedMetrics 获取所有激活的支持指标
func (r *supportedMetricRepositoryImpl) GetActiveSupportedMetrics() ([]*model.SupportedMetric, error) {
	var metrics []*model.SupportedMetric
	result := r.db.Where("is_active = ?", true).Find(&metrics)
	if result.Error != nil {
		return nil, fmt.Errorf("查询激活的支持指标失败: %w", result.Error)
	}
	return metrics, nil
}
