package repository

import (
	"fmt"

	"aiops/control_plane/internal/model"

	"gorm.io/gorm"
)

// DeviceRepository 接口定义了 Device 数据的存储操作
type DeviceRepository interface {
	CreateDevice(device *model.Device) error
	GetDeviceByID(deviceID uint) (*model.Device, error)
	UpdateDevice(device *model.Device) error
	DeleteDevice(deviceID uint) error
	GetAllDevices() ([]*model.Device, error)
	GetDevicesByAgentID(agentID string) ([]*model.Device, error)
}

// deviceRepositoryImpl 是 DeviceRepository 接口的实现
type deviceRepositoryImpl struct {
	db *gorm.DB
}

// NewDeviceRepository 创建一个新的 DeviceRepository 实例
func NewDeviceRepository(db *gorm.DB) DeviceRepository {
	return &deviceRepositoryImpl{db: db}
}

// CreateDevice 创建一个新的 Device 记录
func (r *deviceRepositoryImpl) CreateDevice(device *model.Device) error {
	result := r.db.Create(device)
	if result.Error != nil {
		return fmt.Errorf("创建设备失败: %w", result.Error)
	}
	return nil
}

// GetDeviceByID 根据 ID 获取设备
func (r *deviceRepositoryImpl) GetDeviceByID(deviceID uint) (*model.Device, error) {
	var device model.Device
	result := r.db.First(&device, deviceID)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询设备失败: %w", result.Error)
	}
	return &device, nil
}

// UpdateDevice 更新设备信息
func (r *deviceRepositoryImpl) UpdateDevice(device *model.Device) error {
	result := r.db.Save(device)
	if result.Error != nil {
		return fmt.Errorf("更新设备失败: %w", result.Error)
	}
	return nil
}

// DeleteDevice 删除设备
func (r *deviceRepositoryImpl) DeleteDevice(deviceID uint) error {
	result := r.db.Delete(&model.Device{}, deviceID)
	if result.Error != nil {
		return fmt.Errorf("删除设备失败: %w", result.Error)
	}
	return nil
}

// GetAllDevices 获取所有设备
func (r *deviceRepositoryImpl) GetAllDevices() ([]*model.Device, error) {
	var devices []*model.Device
	result := r.db.Find(&devices)
	if result.Error != nil {
		return nil, fmt.Errorf("查询所有设备失败: %w", result.Error)
	}
	return devices, nil
}

// GetDevicesByAgentID 根据 AgentID 获取设备
func (r *deviceRepositoryImpl) GetDevicesByAgentID(agentID string) ([]*model.Device, error) {
	var devices []*model.Device
	result := r.db.Where("agent_id = ?", agentID).Find(&devices)
	if result.Error != nil {
		return nil, fmt.Errorf("根据 AgentID 查询设备失败: %w", result.Error)
	}
	return devices, nil
}
