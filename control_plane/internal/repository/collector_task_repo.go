package repository

import (
	"fmt"
	"time"

	"aiops/control_plane/internal/model"

	"gorm.io/gorm"
)

// CollectorTaskRepository 接口定义了采集任务数据的存储操作
type CollectorTaskRepository interface {
	CreateTask(task *model.CollectorTask) error
	GetTaskByID(taskID string) (*model.CollectorTask, error)
	GetTaskByTaskID(taskID string) (*model.CollectorTask, error)
	UpdateTask(task *model.CollectorTask) error
	DeleteTask(taskID string) error
	GetAllTasks() ([]*model.CollectorTask, error)
	GetTasksByAgentID(agentID string) ([]*model.CollectorTask, error)
	GetTasksByDeviceID(deviceID uint) ([]*model.CollectorTask, error)
	UpdateTaskStatus(taskID, status, errorMsg string, lastCollectTime int64) error
}

// collectorTaskRepositoryImpl 是 CollectorTaskRepository 接口的实现
type collectorTaskRepositoryImpl struct {
	db *gorm.DB
}

// NewCollectorTaskRepository 创建一个新的 CollectorTaskRepository 实例
func NewCollectorTaskRepository(db *gorm.DB) CollectorTaskRepository {
	return &collectorTaskRepositoryImpl{db: db}
}

// CreateTask 创建一个新的采集任务
func (r *collectorTaskRepositoryImpl) CreateTask(task *model.CollectorTask) error {
	result := r.db.Create(task)
	if result.Error != nil {
		return fmt.Errorf("创建采集任务失败: %w", result.Error)
	}
	return nil
}

// GetTaskByID 根据数据库 ID 获取采集任务
func (r *collectorTaskRepositoryImpl) GetTaskByID(taskID string) (*model.CollectorTask, error) {
	var task model.CollectorTask
	result := r.db.First(&task, taskID)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询采集任务失败: %w", result.Error)
	}
	return &task, nil
}

// GetTaskByTaskID 根据 TaskID 获取采集任务
func (r *collectorTaskRepositoryImpl) GetTaskByTaskID(taskID string) (*model.CollectorTask, error) {
	var task model.CollectorTask
	result := r.db.Where("task_id = ?", taskID).First(&task)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("根据 TaskID 查询采集任务失败: %w", result.Error)
	}
	return &task, nil
}

// UpdateTask 更新采集任务
func (r *collectorTaskRepositoryImpl) UpdateTask(task *model.CollectorTask) error {
	result := r.db.Save(task)
	if result.Error != nil {
		return fmt.Errorf("更新采集任务失败: %w", result.Error)
	}
	return nil
}

// DeleteTask 删除采集任务
func (r *collectorTaskRepositoryImpl) DeleteTask(taskID string) error {
	result := r.db.Where("task_id = ?", taskID).Delete(&model.CollectorTask{})
	if result.Error != nil {
		return fmt.Errorf("删除采集任务失败: %w", result.Error)
	}
	return nil
}

// GetAllTasks 获取所有采集任务
func (r *collectorTaskRepositoryImpl) GetAllTasks() ([]*model.CollectorTask, error) {
	var tasks []*model.CollectorTask
	result := r.db.Find(&tasks)
	if result.Error != nil {
		return nil, fmt.Errorf("查询所有采集任务失败: %w", result.Error)
	}
	return tasks, nil
}

// GetTasksByAgentID 根据 AgentID 获取采集任务
func (r *collectorTaskRepositoryImpl) GetTasksByAgentID(agentID string) ([]*model.CollectorTask, error) {
	var tasks []*model.CollectorTask
	result := r.db.Where("agent_id = ?", agentID).Find(&tasks)
	if result.Error != nil {
		return nil, fmt.Errorf("根据 AgentID 查询采集任务失败: %w", result.Error)
	}
	return tasks, nil
}

// GetTasksByDeviceID 根据 DeviceID 获取采集任务
func (r *collectorTaskRepositoryImpl) GetTasksByDeviceID(deviceID uint) ([]*model.CollectorTask, error) {
	var tasks []*model.CollectorTask
	result := r.db.Where("device_id = ?", deviceID).Find(&tasks)
	if result.Error != nil {
		return nil, fmt.Errorf("根据 DeviceID 查询采集任务失败: %w", result.Error)
	}
	return tasks, nil
}

// UpdateTaskStatus 更新采集任务状态
func (r *collectorTaskRepositoryImpl) UpdateTaskStatus(taskID, status, errorMsg string, lastCollectTime int64) error {
	result := r.db.Model(&model.CollectorTask{}).
		Where("task_id = ?", taskID).
		Updates(map[string]interface{}{
			"status":            status,
			"last_error":        errorMsg,
			"last_collect_time": time.Unix(lastCollectTime, 0),
		})
	if result.Error != nil {
		return fmt.Errorf("更新任务状态失败: %w", result.Error)
	}
	return nil
}
