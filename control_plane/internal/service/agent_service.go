package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"
	"aiops/pkg/log"

	"go.uber.org/zap"
)

// AgentService 提供 Agent 管理相关的业务逻辑
type AgentService struct {
	agentRepo    repository.AgentRepository
	deviceRepo   repository.DeviceRepository
	taskRepo     repository.CollectorTaskRepository
	logger       *log.Logger
	agentMutex   sync.RWMutex
	agentStreams map[string]AgentStream // 存储每个 Agent 的 gRPC 流
}

// AgentStream 包含与 Agent 通信的流
type AgentStream struct {
	TaskStatusCh chan model.TaskStatus
	TaskConfigCh chan model.CollectorTaskConfig
}

// NewAgentService 创建一个新的 AgentService 实例
func NewAgentService(
	agentRepo repository.AgentRepository,
	deviceRepo repository.DeviceRepository,
	taskRepo repository.CollectorTaskRepository,
	logger *log.Logger,
) *AgentService {
	return &AgentService{
		agentRepo:    agentRepo,
		deviceRepo:   deviceRepo,
		taskRepo:     taskRepo,
		logger:       logger,
		agentStreams: make(map[string]AgentStream),
		agentMutex:   sync.RWMutex{},
	}
}

// RegisterAgent 注册新的 Agent 或更新现有 Agent
func (s *AgentService) RegisterAgent(ctx context.Context, agentID, agentIP string, supportedCollectorTypes []string) (bool, error) {
	s.logger.Info("注册 Agent: ", zap.String("AgentID", agentID), zap.String("IP", agentIP),
		zap.Strings("支持的采集器类型", supportedCollectorTypes))

	// 查询 Agent 是否已存在
	agent, err := s.agentRepo.GetAgentByID(agentID)
	if err != nil {
		return false, fmt.Errorf("查询 Agent 失败: %w", err)
	}

	// 设置 Agent 信息
	now := time.Now()
	if agent == nil {
		// 创建新 Agent
		agent = &model.Agent{
			AgentID:                 agentID,
			AgentIP:                 agentIP,
			SupportedCollectorTypes: formatCollectorTypes(supportedCollectorTypes),
			Status:                  "online",
			LastHeartbeat:           now,
		}
		if err := s.agentRepo.CreateAgent(agent); err != nil {
			return false, fmt.Errorf("创建 Agent 失败: %w", err)
		}
		s.logger.Info("新 Agent 注册成功", zap.String("agentID", agentID))

	} else {
		// 更新现有 Agent
		agent.AgentIP = agentIP
		agent.SupportedCollectorTypes = formatCollectorTypes(supportedCollectorTypes)
		agent.Status = "online"
		agent.LastHeartbeat = now
		if err := s.agentRepo.UpdateAgent(agent); err != nil {
			return false, fmt.Errorf("更新 Agent 失败: %w", err)
		}
		s.logger.Info("更新 Agent 信息", zap.String("agentID", agentID))
	}

	return true, nil
}

// GetAgent 获取 Agent 信息
func (s *AgentService) GetAgent(agentID string) (*model.Agent, error) {
	agent, err := s.agentRepo.GetAgentByID(agentID)
	if err != nil {
		return nil, fmt.Errorf("获取 Agent 信息失败: %w", err)
	}
	return agent, nil
}

// GetAllAgents 获取所有 Agent
func (s *AgentService) GetAllAgents() ([]*model.Agent, error) {
	agents, err := s.agentRepo.GetAllAgents()
	if err != nil {
		return nil, fmt.Errorf("获取所有 Agent 失败: %w", err)
	}
	return agents, nil
}

// DeleteAgent 删除 Agent
func (s *AgentService) DeleteAgent(agentID string) error {
	// 检查是否存在依赖该 Agent 的设备或任务
	devices, err := s.deviceRepo.GetDevicesByAgentID(agentID)
	if err != nil {
		return fmt.Errorf("查询 Agent 关联设备失败: %w", err)
	}
	if len(devices) > 0 {
		return fmt.Errorf("无法删除 Agent，还有 %d 个设备与其关联", len(devices))
	}

	// 删除 Agent
	if err := s.agentRepo.DeleteAgent(agentID); err != nil {
		return fmt.Errorf("删除 Agent 失败: %w", err)
	}

	// 关闭流 (如果存在)
	s.closeAgentStream(agentID)

	s.logger.Info("删除 Agent 成功", zap.String("agentID", agentID))
	return nil
}

// RegisterAgentStream 注册 Agent 的流通道
func (s *AgentService) RegisterAgentStream(agentID string) (AgentStream, error) {
	// 检查 Agent 是否存在
	agent, err := s.agentRepo.GetAgentByID(agentID)
	if err != nil {
		return AgentStream{}, fmt.Errorf("查询 Agent 失败: %w", err)
	}
	if agent == nil {
		return AgentStream{}, fmt.Errorf("Agent 不存在: %s", agentID)
	}

	// 创建新的流
	stream := AgentStream{
		TaskStatusCh: make(chan model.TaskStatus, 100),
		TaskConfigCh: make(chan model.CollectorTaskConfig, 100),
	}

	// 存储流
	s.agentMutex.Lock()
	defer s.agentMutex.Unlock()

	// 关闭旧的流 (如果存在)
	if oldStream, exists := s.agentStreams[agentID]; exists {
		close(oldStream.TaskStatusCh)
		close(oldStream.TaskConfigCh)
	}

	s.agentStreams[agentID] = stream
	s.logger.Info("Agent 流注册成功", zap.String("agentID", agentID))

	// 查询 Agent 关联的任务，并发送到新流
	tasks, err := s.taskRepo.GetTasksByAgentID(agentID)
	if err != nil {
		s.logger.Error("查询 Agent 任务失败", zap.String("agentID", agentID), zap.Error(err))
	} else {
		// 在后台发送任务配置
		go func() {
			for _, task := range tasks {
				if !task.IsEnabled {
					continue
				}

				// 转换为配置
				config := s.convertTaskToConfig(*task)

				// 尝试发送，但不阻塞太久
				select {
				case stream.TaskConfigCh <- config:
					s.logger.Info("向 Agent 发送任务配置", zap.String("agentID", agentID), zap.String("taskID", task.TaskID))
				case <-time.After(1 * time.Second):
					s.logger.Error("向 Agent 发送任务配置超时", zap.String("agentID", agentID), zap.String("taskID", task.TaskID))
				}
			}
		}()
	}

	return stream, nil
}

// UpdateTaskStatus 更新任务状态
func (s *AgentService) UpdateTaskStatus(agentID string, taskStatus model.TaskStatus) error {
	// 更新数据库中的任务状态
	// 将 time.Time 转换为 Unix 时间戳 (int64)
	lastCollectTimestamp := taskStatus.LastCollectTimestamp.Unix()

	err := s.taskRepo.UpdateTaskStatus(
		taskStatus.TaskID,
		taskStatus.Status,
		taskStatus.ErrorMessage,
		lastCollectTimestamp,
	)
	if err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	s.logger.Info("更新任务状态",
		zap.String("agentID", agentID),
		zap.String("taskID", taskStatus.TaskID),
		zap.String("状态", taskStatus.Status))

	return nil
}

// GetAgentStream 获取 Agent 的流通道
func (s *AgentService) GetAgentStream(agentID string) (AgentStream, bool) {
	s.agentMutex.RLock()
	defer s.agentMutex.RUnlock()

	stream, exists := s.agentStreams[agentID]
	return stream, exists
}

// closeAgentStream 关闭并删除 Agent 的流通道
func (s *AgentService) closeAgentStream(agentID string) {
	s.agentMutex.Lock()
	defer s.agentMutex.Unlock()

	if stream, exists := s.agentStreams[agentID]; exists {
		close(stream.TaskStatusCh)
		close(stream.TaskConfigCh)
		delete(s.agentStreams, agentID)
		s.logger.Info("关闭 Agent 流", zap.String("agentID", agentID))
	}
}

// UpdateTaskConfiguration 更新任务配置并发送到 Agent
func (s *AgentService) UpdateTaskConfiguration(task *model.CollectorTask) error {
	// 保存到数据库
	if err := s.taskRepo.UpdateTask(task); err != nil {
		return fmt.Errorf("更新任务配置失败: %w", err)
	}

	// 如果任务已启用，发送到相关 Agent
	if task.IsEnabled {
		agentID := task.AgentID
		s.agentMutex.RLock()
		stream, exists := s.agentStreams[agentID]
		s.agentMutex.RUnlock()

		if exists {
			// 转换为配置并发送
			config := s.convertTaskToConfig(*task)

			// 以非阻塞方式发送
			select {
			case stream.TaskConfigCh <- config:
				s.logger.Info("发送任务配置更新", zap.String("taskID", task.TaskID), zap.String("agentID", agentID))
			case <-time.After(1 * time.Second):
				s.logger.Error("发送任务配置超时", zap.String("taskID", task.TaskID), zap.String("agentID", agentID))
			}
		} else {
			s.logger.Info("Agent 当前未连接，配置将在重连时发送", zap.String("taskID", task.TaskID), zap.String("agentID", agentID))
		}
	}

	return nil
}

// formatCollectorTypes 将收集器类型数组格式化为字符串
func formatCollectorTypes(types []string) string {
	// 实际中应该使用 JSON 封装
	return fmt.Sprintf("%v", types)
}

// convertTaskToConfig 将任务模型转换为配置消息
func (s *AgentService) convertTaskToConfig(task model.CollectorTask) model.CollectorTaskConfig {
	// 查询设备信息
	device, err := s.deviceRepo.GetDeviceByID(task.DeviceID)
	if err != nil {
		s.logger.Error("查询设备失败", zap.Error(err))
	}
	if device == nil {
		s.logger.Error("设备不存在", zap.Uint("deviceID", task.DeviceID))
		device = &model.Device{} // 使用空设备避免空指针
	}

	// 创建任务配置
	config := model.CollectorTaskConfig{
		TaskID:       task.TaskID,
		DeviceID:     fmt.Sprintf("%d", task.DeviceID),
		DeviceName:   device.Name,
		DeviceType:   device.Type,
		Host:         device.Host,
		Port:         device.Port,
		Username:     device.Username,
		Password:     device.Password,
		CollectItems: task.CollectItems,
		IsEnabled:    task.IsEnabled,
		Frequency:    task.Frequency,
	}

	return config
}
