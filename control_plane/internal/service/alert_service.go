package service

import (
	"encoding/json"
	"fmt"
	"net/smtp"
	"strings"
	"time"

	"aiops/control_plane/config"
	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"
	"aiops/pkg/log"

	"go.uber.org/zap"
)

// AlertService 提供告警相关的业务逻辑
type AlertService struct {
	alertRuleRepo  repository.AlertRuleRepository
	alertEventRepo repository.AlertEventRepository
	deviceRepo     repository.DeviceRepository
	config         *config.Config
	logger         *log.Logger
}

// NewAlertService 创建一个新的 AlertService 实例
func NewAlertService(
	alertRuleRepo repository.AlertRuleRepository,
	alertEventRepo repository.AlertEventRepository,
	deviceRepo repository.DeviceRepository,
	config *config.Config,
	logger *log.Logger,
) *AlertService {
	return &AlertService{
		alertRuleRepo:  alertRuleRepo,
		alertEventRepo: alertEventRepo,
		deviceRepo:     deviceRepo,
		config:         config,
		logger:         logger,
	}
}

// CreateAlertRule 创建告警规则
func (s *AlertService) CreateAlertRule(rule *model.AlertRule) error {
	// 检查设备是否存在
	device, err := s.deviceRepo.GetDeviceByID(rule.DeviceID)
	if err != nil {
		return fmt.Errorf("查询设备失败: %w", err)
	}
	if device == nil {
		return fmt.Errorf("设备不存在: %d", rule.DeviceID)
	}

	// 保存告警规则
	if err := s.alertRuleRepo.CreateAlertRule(rule); err != nil {
		return fmt.Errorf("创建告警规则失败: %w", err)
	}

	s.logger.Info("创建告警规则成功",
		zap.String("ruleName", rule.RuleName),
		zap.Uint("id", rule.ID))
	return nil
}

// UpdateAlertRule 更新告警规则
func (s *AlertService) UpdateAlertRule(rule *model.AlertRule) error {
	// 检查规则是否存在
	existingRule, err := s.alertRuleRepo.GetAlertRuleByID(rule.ID)
	if err != nil {
		return fmt.Errorf("查询告警规则失败: %w", err)
	}
	if existingRule == nil {
		return fmt.Errorf("告警规则不存在: %d", rule.ID)
	}

	// 检查设备是否存在
	device, err := s.deviceRepo.GetDeviceByID(rule.DeviceID)
	if err != nil {
		return fmt.Errorf("查询设备失败: %w", err)
	}
	if device == nil {
		return fmt.Errorf("设备不存在: %d", rule.DeviceID)
	}

	// 更新告警规则
	if err := s.alertRuleRepo.UpdateAlertRule(rule); err != nil {
		return fmt.Errorf("更新告警规则失败: %w", err)
	}

	s.logger.Info("更新告警规则成功", zap.String("ruleName", rule.RuleName), zap.Uint("ID", rule.ID))
	return nil
}

// DeleteAlertRule 删除告警规则
func (s *AlertService) DeleteAlertRule(id uint) error {
	// 检查规则是否存在
	rule, err := s.alertRuleRepo.GetAlertRuleByID(id)
	if err != nil {
		return fmt.Errorf("查询告警规则失败: %w", err)
	}
	if rule == nil {
		return fmt.Errorf("告警规则不存在: %d", id)
	}

	// 删除告警规则
	if err := s.alertRuleRepo.DeleteAlertRule(id); err != nil {
		return fmt.Errorf("删除告警规则失败: %w", err)
	}

	s.logger.Info("删除告警规则成功", zap.Uint("id", id))
	return nil
}

// GetAlertRule 获取告警规则
func (s *AlertService) GetAlertRule(id uint) (*model.AlertRule, error) {
	rule, err := s.alertRuleRepo.GetAlertRuleByID(id)
	if err != nil {
		return nil, fmt.Errorf("查询告警规则失败: %w", err)
	}
	if rule == nil {
		return nil, fmt.Errorf("告警规则不存在: %d", id)
	}
	return rule, nil
}

// GetAllAlertRules 获取所有告警规则
func (s *AlertService) GetAllAlertRules() ([]*model.AlertRule, error) {
	rules, err := s.alertRuleRepo.GetAllAlertRules()
	if err != nil {
		return nil, fmt.Errorf("查询所有告警规则失败: %w", err)
	}
	return rules, nil
}

// GetDeviceAlertRules 获取设备的告警规则
func (s *AlertService) GetDeviceAlertRules(deviceID uint) ([]*model.AlertRule, error) {
	rules, err := s.alertRuleRepo.GetAlertRulesByDeviceID(deviceID)
	if err != nil {
		return nil, fmt.Errorf("查询设备告警规则失败: %w", err)
	}
	return rules, nil
}

// GetAlertEvents 获取告警事件
func (s *AlertService) GetAlertEvents(startTime, endTime time.Time, limit int) ([]*model.AlertEvent, error) {
	events, err := s.alertEventRepo.GetAlertEventsByTimeRange(startTime, endTime, limit)
	if err != nil {
		return nil, fmt.Errorf("查询告警事件失败: %w", err)
	}
	return events, nil
}

// GetActiveAlerts 获取当前活跃的告警
func (s *AlertService) GetActiveAlerts() ([]*model.AlertEvent, error) {
	events, err := s.alertEventRepo.GetActiveAlertEvents()
	if err != nil {
		return nil, fmt.Errorf("查询活跃告警失败: %w", err)
	}
	return events, nil
}

// ResolveAlert 解决告警
func (s *AlertService) ResolveAlert(id uint) error {
	event, err := s.alertEventRepo.GetAlertEventByID(id)
	if err != nil {
		return fmt.Errorf("查询告警事件失败: %w", err)
	}
	if event == nil {
		return fmt.Errorf("告警事件不存在: %d", id)
	}

	// 更新状态为已解决
	now := time.Now()
	event.Status = "resolved"
	event.ResolvedTime = &now

	if err := s.alertEventRepo.UpdateAlertEvent(event); err != nil {
		return fmt.Errorf("更新告警事件失败: %w", err)
	}

	s.logger.Info("解决告警成功", zap.Uint("id", id))
	return nil
}

// SendAlert 发送告警通知
func (s *AlertService) SendAlert(event *model.AlertEvent) error {
	// 获取告警规则
	rule, err := s.alertRuleRepo.GetAlertRuleByID(event.RuleID)
	if err != nil {
		s.logger.Error("获取告警规则失败", zap.Uint("ruleID", event.RuleID), zap.Error(err))
		return fmt.Errorf("获取告警规则失败: %w", err)
	}

	// 获取设备信息
	device, err := s.deviceRepo.GetDeviceByID(event.DeviceID)
	if err != nil {
		s.logger.Error("获取设备信息失败", zap.Uint("deviceID", event.DeviceID), zap.Error(err))
		return fmt.Errorf("获取设备信息失败: %w", err)
	}

	// 解析通知渠道
	channels := make([]string, 0)
	if rule.NotificationChannels != "" {
		if err := json.Unmarshal([]byte(rule.NotificationChannels), &channels); err != nil {
			s.logger.Error("解析通知渠道失败", zap.Error(err))
			// 继续执行，使用默认渠道
		}
	}

	// 如果没有指定渠道，默认使用邮件
	if len(channels) == 0 {
		channels = append(channels, "email")
	}

	// 构建告警消息
	deviceName := "未知设备"
	if device != nil {
		deviceName = device.Name
	}

	subject := fmt.Sprintf("[告警] %s: %s", deviceName, rule.RuleName)
	message := fmt.Sprintf(
		"告警时间: %s\n"+
			"设备名称: %s\n"+
			"告警规则: %s\n"+
			"指标名称: %s\n"+
			"触发值: %.2f %s %.2f\n"+
			"告警级别: %s\n"+
			"告警详情: %s\n",
		event.TriggerTime.Format("2006-01-02 15:04:05"),
		deviceName,
		rule.RuleName,
		event.MetricKey,
		event.TriggerValue, rule.ThresholdOp, rule.ThresholdValue,
		event.Severity,
		event.Message,
	)

	// 发送通知
	for _, channel := range channels {
		switch channel {
		case "email":
			if err := s.sendEmailAlert(subject, message); err != nil {
				s.logger.Error("发送邮件告警失败", zap.Error(err))
			}
		// 可以添加其他通知渠道，如短信、钉钉等
		default:
			s.logger.Error("不支持的通知渠道", zap.String("channel", channel))
		}
	}

	return nil
}

// sendEmailAlert 发送邮件告警
func (s *AlertService) sendEmailAlert(subject, body string) error {
	// 检查邮件配置是否完整
	if s.config.EmailSender == "" || s.config.EmailPassword == "" || s.config.EmailSMTPHost == "" {
		return fmt.Errorf("邮件配置不完整，无法发送邮件告警")
	}

	// 构建邮件内容
	to := []string{s.config.EmailSender} // 使用发件人地址作为收件人（示例）
	msg := []byte("To: " + strings.Join(to, ",") + "\r\n" +
		"Subject: " + subject + "\r\n" +
		"Content-Type: text/plain; charset=UTF-8\r\n" +
		"\r\n" +
		body + "\r\n")

	// 发送邮件
	auth := smtp.PlainAuth("", s.config.EmailSender, s.config.EmailPassword, s.config.EmailSMTPHost)
	addr := fmt.Sprintf("%s:%d", s.config.EmailSMTPHost, s.config.EmailSMTPPort)

	err := smtp.SendMail(addr, auth, s.config.EmailSender, to, msg)
	if err != nil {
		return fmt.Errorf("发送邮件失败: %w", err)
	}

	s.logger.Info("成功发送邮件告警", zap.String("subject", subject))
	return nil
}
