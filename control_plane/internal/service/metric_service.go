package service

import (
	"encoding/json"
	"fmt"
	"time"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"
	"aiops/pkg/log"

	"go.uber.org/zap"
)

// MetricService 提供指标数据相关的业务逻辑
type MetricService struct {
	metricRepo     repository.MetricRepository
	deviceRepo     repository.DeviceRepository
	alertRuleRepo  repository.AlertRuleRepository
	alertEventRepo repository.AlertEventRepository
	alertService   *AlertService
	logger         *log.Logger
}

// NewMetricService 创建一个新的 MetricService 实例
func NewMetricService(
	metricRepo repository.MetricRepository,
	deviceRepo repository.DeviceRepository,
	alertRuleRepo repository.AlertRuleRepository,
	alertEventRepo repository.AlertEventRepository,
	logger *log.Logger,
) *MetricService {
	return &MetricService{
		metricRepo:     metricRepo,
		deviceRepo:     deviceRepo,
		alertRuleRepo:  alertRuleRepo,
		alertEventRepo: alertEventRepo,
		logger:         logger,
	}
}

// SetAlertService 设置告警服务
func (s *MetricService) SetAlertService(alertService *AlertService) {
	s.alertService = alertService
}

// SaveMetrics 保存指标数据并触发告警检查
func (s *MetricService) SaveMetrics(metrics []*model.MetricData) error {
	if len(metrics) == 0 {
		return nil
	}

	// 保存指标数据
	if err := s.metricRepo.SaveMetrics(metrics); err != nil {
		return fmt.Errorf("保存指标数据失败: %w", err)
	}

	s.logger.Info("成功保存指标数据", zap.Int("count", len(metrics)))

	// 按设备分组并检查告警
	if s.alertService != nil {
		deviceMetrics := make(map[string][]*model.MetricData)
		for _, metric := range metrics {
			deviceMetrics[metric.DeviceID] = append(deviceMetrics[metric.DeviceID], metric)
		}

		// 对每个设备的指标进行告警检查
		for deviceID, deviceMetrics := range deviceMetrics {
			go s.checkAlertsForDevice(deviceID, deviceMetrics)
		}
	}

	return nil
}

// GetDeviceMetrics 获取设备指标数据
func (s *MetricService) GetDeviceMetrics(deviceID string, startTime, endTime time.Time, limit int) ([]*model.MetricData, error) {
	metrics, err := s.metricRepo.GetMetricsByDeviceID(deviceID, startTime, endTime, limit)
	if err != nil {
		return nil, fmt.Errorf("获取设备指标数据失败: %w", err)
	}
	return metrics, nil
}

// GetMetricByKey 根据指标键获取指标数据
func (s *MetricService) GetMetricByKey(deviceID, metricKey string, startTime, endTime time.Time, limit int) ([]*model.MetricData, error) {
	metrics, err := s.metricRepo.GetMetricsByKey(deviceID, metricKey, startTime, endTime, limit)
	if err != nil {
		return nil, fmt.Errorf("获取指标数据失败: %w", err)
	}
	return metrics, nil
}

// GetLatestMetric 获取最新的指标数据
func (s *MetricService) GetLatestMetric(deviceID, metricKey string) (*model.MetricData, error) {
	metric, err := s.metricRepo.GetLatestMetric(deviceID, metricKey)
	if err != nil {
		return nil, fmt.Errorf("获取最新指标数据失败: %w", err)
	}
	return metric, nil
}

// DeleteOldMetrics 删除旧的指标数据
func (s *MetricService) DeleteOldMetrics(retentionDays int) error {
	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)
	if err := s.metricRepo.DeleteMetricsBeforeTime(cutoffTime); err != nil {
		return fmt.Errorf("删除旧的指标数据失败: %w", err)
	}

	s.logger.Info("删除指标数据", zap.String("前日期", cutoffTime.Format(time.RFC3339)))
	return nil
}

// checkAlertsForDevice 检查设备的告警规则
func (s *MetricService) checkAlertsForDevice(deviceID string, metrics []*model.MetricData) {
	if s.alertService == nil {
		return
	}

	// 解析设备ID为uint
	var deviceIDUint uint
	_, err := fmt.Sscanf(deviceID, "%d", &deviceIDUint)
	if err != nil {
		s.logger.Error("解析设备ID失败", zap.String("deviceID", deviceID), zap.Error(err))
		return
	}

	// 获取设备的告警规则
	rules, err := s.alertRuleRepo.GetAlertRulesByDeviceID(deviceIDUint)
	if err != nil {
		s.logger.Error("获取设备告警规则失败", zap.String("deviceID", deviceID), zap.Error(err))
		return
	}

	// 检查每条指标是否触发告警
	for _, metric := range metrics {
		for _, rule := range rules {
			if !rule.IsEnabled || rule.MetricKey != metric.MetricKey {
				continue
			}

			// 检查阈值
			if s.checkThreshold(rule, metric) {
				// 触发告警
				s.triggerAlert(rule, deviceIDUint, metric)
			}
		}
	}
}

// checkThreshold 检查指标是否超过阈值
func (s *MetricService) checkThreshold(rule *model.AlertRule, metric *model.MetricData) bool {
	// 获取数值类型的指标值
	value := s.getNumericValue(metric)
	if value == nil {
		s.logger.Debug("指标不是数值类型，跳过阈值检查", zap.String("metricKey", metric.MetricKey))
		return false
	}

	switch rule.ThresholdOp {
	case ">":
		return *value > rule.ThresholdValue
	case ">=":
		return *value >= rule.ThresholdValue
	case "<":
		return *value < rule.ThresholdValue
	case "<=":
		return *value <= rule.ThresholdValue
	case "==":
		return *value == rule.ThresholdValue
	default:
		s.logger.Error("不支持的阈值操作符", zap.String("operator", rule.ThresholdOp))
		return false
	}
}

// getNumericValue 从 MetricData 中获取数值类型的值
func (s *MetricService) getNumericValue(metric *model.MetricData) *float64 {
	if metric.NumericValue != nil {
		return metric.NumericValue
	}
	return nil
}

// triggerAlert 触发告警
func (s *MetricService) triggerAlert(rule *model.AlertRule, deviceID uint, metric *model.MetricData) {
	// 检查是否已存在活跃的告警
	events, err := s.alertEventRepo.GetAlertEventsByRuleID(rule.ID, 1)
	if err != nil {
		s.logger.Error("查询现有告警失败", zap.Error(err))
		return
	}

	// 如果存在活跃的告警，不重复触发
	for _, event := range events {
		if event.Status == "firing" {
			s.logger.Info("告警已存在，不重复触发", zap.String("规则", rule.RuleName), zap.Uint("设备", deviceID))
			return
		}
	}

	// 生成告警消息
	labelsMap := make(map[string]string)
	if metric.Labels != "" {
		if err := json.Unmarshal([]byte(metric.Labels), &labelsMap); err != nil {
			s.logger.Error("解析标签失败", zap.Error(err))
		}
	}

	// 获取用于告警的数值
	value := s.getNumericValue(metric)
	if value == nil {
		s.logger.Error("无法获取指标数值用于告警", zap.String("metricKey", metric.MetricKey))
		return
	}

	message := fmt.Sprintf("指标 %s 的值 %.2f %s %.2f",
		metric.MetricKey, *value, rule.ThresholdOp, rule.ThresholdValue)

	// 创建告警事件
	event := &model.AlertEvent{
		RuleID:       rule.ID,
		DeviceID:     deviceID,
		MetricKey:    metric.MetricKey,
		Severity:     "critical", // 可以从规则配置中获取
		Message:      message,
		TriggerValue: *value,
		TriggerTime:  time.Unix(metric.Timestamp, 0),
		Status:       "firing",
		ResolvedTime: nil,
	}

	if err := s.alertEventRepo.CreateAlertEvent(event); err != nil {
		s.logger.Error("创建告警事件失败", zap.Error(err))
		return
	}

	s.logger.Info("触发告警", zap.String("message", message))

	// 通过告警服务发送通知
	if s.alertService != nil {
		go s.alertService.SendAlert(event)
	}
}
