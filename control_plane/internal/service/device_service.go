package service

import (
	"fmt"
	"time"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"
	"aiops/pkg/log"

	"go.uber.org/zap"
)

// DeviceService 提供设备管理相关的业务逻辑
type DeviceService struct {
	deviceRepo repository.DeviceRepository
	taskRepo   repository.CollectorTaskRepository
	agentRepo  repository.AgentRepository
	logger     *log.Logger
}

// NewDeviceService 创建一个新的 DeviceService 实例
func NewDeviceService(
	deviceRepo repository.DeviceRepository,
	taskRepo repository.CollectorTaskRepository,
	agentRepo repository.AgentRepository,
	logger *log.Logger,
) *DeviceService {
	return &DeviceService{
		deviceRepo: deviceRepo,
		taskRepo:   taskRepo,
		agentRepo:  agentRepo,
		logger:     logger,
	}
}

// CreateDevice 创建新设备
func (s *DeviceService) CreateDevice(device *model.Device) error {
	// 设置默认值
	device.Status = "unknown"

	// 检查指定的 AgentID 是否存在
	if device.AgentID != "" {
		agent, err := s.agentRepo.GetAgentByID(device.AgentID)
		if err != nil {
			return fmt.Errorf("查询 Agent 失败: %w", err)
		}
		if agent == nil {
			return fmt.Errorf("指定的 Agent 不存在: %s", device.AgentID)
		}
	}

	// 保存设备信息
	if err := s.deviceRepo.CreateDevice(device); err != nil {
		return fmt.Errorf("创建设备失败: %w", err)
	}

	s.logger.Info("创建设备成功", zap.String("name", device.Name), zap.Uint("id", device.ID))
	return nil
}

// GetDevice 获取设备信息
func (s *DeviceService) GetDevice(id uint) (*model.Device, error) {
	device, err := s.deviceRepo.GetDeviceByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取设备信息失败: %w", err)
	}
	if device == nil {
		return nil, fmt.Errorf("设备不存在: %d", id)
	}
	return device, nil
}

// UpdateDevice 更新设备信息
func (s *DeviceService) UpdateDevice(device *model.Device) error {
	// 检查设备是否存在
	existingDevice, err := s.deviceRepo.GetDeviceByID(device.ID)
	if err != nil {
		return fmt.Errorf("查询设备失败: %w", err)
	}
	if existingDevice == nil {
		return fmt.Errorf("设备不存在: %d", device.ID)
	}

	// 检查 Agent 是否存在
	if device.AgentID != "" {
		agent, err := s.agentRepo.GetAgentByID(device.AgentID)
		if err != nil {
			return fmt.Errorf("查询 Agent 失败: %w", err)
		}
		if agent == nil {
			return fmt.Errorf("指定的 Agent 不存在: %s", device.AgentID)
		}
	}

	// 保留一些不应由用户更新的字段
	device.Status = existingDevice.Status
	device.LastCollectTime = existingDevice.LastCollectTime

	// 更新设备信息
	if err := s.deviceRepo.UpdateDevice(device); err != nil {
		return fmt.Errorf("更新设备失败: %w", err)
	}

	// 如果 AgentID 发生变化，需要更新相关任务
	if device.AgentID != existingDevice.AgentID {
		tasks, err := s.taskRepo.GetTasksByDeviceID(device.ID)
		if err != nil {
			s.logger.Error("查询设备相关任务失败", zap.Uint("deviceID", device.ID), zap.Error(err))
		} else {
			for _, task := range tasks {
				task.AgentID = device.AgentID
				if err := s.taskRepo.UpdateTask(task); err != nil {
					s.logger.Error("更新任务 Agent ID 失败", zap.String("taskID", task.TaskID), zap.Error(err))
				}
			}
			s.logger.Info("已更新设备的任务 Agent ID", zap.Uint("deviceID", device.ID), zap.Int("taskCount", len(tasks)))
		}
	}

	s.logger.Info("更新设备成功", zap.String("name", device.Name), zap.Uint("id", device.ID))
	return nil
}

// DeleteDevice 删除设备
func (s *DeviceService) DeleteDevice(id uint) error {
	// 检查是否存在关联的采集任务
	tasks, err := s.taskRepo.GetTasksByDeviceID(id)
	if err != nil {
		return fmt.Errorf("查询设备相关任务失败: %w", err)
	}
	if len(tasks) > 0 {
		return fmt.Errorf("无法删除设备，还有 %d 个任务与其关联", len(tasks))
	}

	// 删除设备
	if err := s.deviceRepo.DeleteDevice(id); err != nil {
		return fmt.Errorf("删除设备失败: %w", err)
	}

	s.logger.Info("删除设备成功", zap.Uint("id", id))
	return nil
}

// GetAllDevices 获取所有设备
func (s *DeviceService) GetAllDevices() ([]*model.Device, error) {
	devices, err := s.deviceRepo.GetAllDevices()
	if err != nil {
		return nil, fmt.Errorf("获取所有设备失败: %w", err)
	}
	return devices, nil
}

// GetDevicesByAgent 获取指定 Agent 的设备
func (s *DeviceService) GetDevicesByAgent(agentID string) ([]*model.Device, error) {
	devices, err := s.deviceRepo.GetDevicesByAgentID(agentID)
	if err != nil {
		return nil, fmt.Errorf("获取 Agent 设备失败: %w", err)
	}
	return devices, nil
}

// UpdateDeviceStatus 更新设备状态
func (s *DeviceService) UpdateDeviceStatus(id uint, status string, lastCollectTime time.Time) error {
	device, err := s.deviceRepo.GetDeviceByID(id)
	if err != nil {
		return fmt.Errorf("获取设备信息失败: %w", err)
	}
	if device == nil {
		return fmt.Errorf("设备不存在: %d", id)
	}

	device.Status = status
	device.LastCollectTime = lastCollectTime

	if err := s.deviceRepo.UpdateDevice(device); err != nil {
		return fmt.Errorf("更新设备状态失败: %w", err)
	}

	s.logger.Info("更新设备状态", zap.Uint("id", id), zap.String("status", status))
	return nil
}
