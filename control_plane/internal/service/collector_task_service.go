package service

import (
	"fmt"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"
	"aiops/pkg/log"
)

// CollectorTaskService 提供采集任务相关的业务逻辑
type CollectorTaskService struct {
	taskRepo     repository.CollectorTaskRepository
	deviceRepo   repository.DeviceRepository
	agentRepo    repository.AgentRepository
	agentService *AgentService
	logger       *log.Logger
}

// NewCollectorTaskService 创建一个新的 CollectorTaskService 实例
func NewCollectorTaskService(
	taskRepo repository.CollectorTaskRepository,
	deviceRepo repository.DeviceRepository,
	agentRepo repository.AgentRepository,
	logger *log.Logger,
) *CollectorTaskService {
	return &CollectorTaskService{
		taskRepo:   taskRepo,
		deviceRepo: deviceRepo,
		agentRepo:  agentRepo,
		logger:     logger,
	}
}

// SetAgentService 设置 AgentService
func (s *CollectorTaskService) SetAgentService(agentService *AgentService) {
	s.agentService = agentService
}

// CreateTask 创建采集任务
func (s *CollectorTaskService) CreateTask(task *model.CollectorTask) error {
	// 检查设备是否存在
	device, err := s.deviceRepo.GetDeviceByID(task.DeviceID)
	if err != nil {
		return fmt.Errorf("查询设备失败: %w", err)
	}
	if device == nil {
		return fmt.Errorf("设备不存在: %d", task.DeviceID)
	}

	// 使用设备的 AgentID（如果未指定）
	if task.AgentID == "" {
		task.AgentID = device.AgentID
	}

	// 检查 Agent 是否存在
	if task.AgentID != "" {
		agent, err := s.agentRepo.GetAgentByID(task.AgentID)
		if err != nil {
			return fmt.Errorf("查询 Agent 失败: %w", err)
		}
		if agent == nil {
			return fmt.Errorf("Agent 不存在: %s", task.AgentID)
		}
	} else {
		return fmt.Errorf("未指定 AgentID，无法创建任务")
	}

	// 生成唯一的任务 ID
	task.TaskID = uuid.New().String()
	task.Status = "stopped"

	// 保存任务
	if err := s.taskRepo.CreateTask(task); err != nil {
		return fmt.Errorf("创建任务失败: %w", err)
	}

	s.logger.Info("创建采集任务成功", zap.String("taskName", task.TaskName), zap.String("taskID", task.TaskID))

	// 如果任务已启用，通过 AgentService 发送配置
	if task.IsEnabled && s.agentService != nil {
		if err := s.agentService.UpdateTaskConfiguration(task); err != nil {
			s.logger.Error("发送任务配置失败", zap.String("taskID", task.TaskID), zap.Error(err))
		}
	}

	return nil
}

// UpdateTask 更新采集任务
func (s *CollectorTaskService) UpdateTask(task *model.CollectorTask) error {
	// 检查任务是否存在
	existingTask, err := s.taskRepo.GetTaskByID(task.TaskID)
	if err != nil {
		return fmt.Errorf("查询任务失败: %w", err)
	}
	if existingTask == nil {
		return fmt.Errorf("任务不存在: %s", task.TaskID)
	}

	// 检查设备是否存在
	device, err := s.deviceRepo.GetDeviceByID(task.DeviceID)
	if err != nil {
		return fmt.Errorf("查询设备失败: %w", err)
	}
	if device == nil {
		return fmt.Errorf("设备不存在: %d", task.DeviceID)
	}

	// 检查 Agent 是否存在
	if task.AgentID != "" {
		agent, err := s.agentRepo.GetAgentByID(task.AgentID)
		if err != nil {
			return fmt.Errorf("查询 Agent 失败: %w", err)
		}
		if agent == nil {
			return fmt.Errorf("Agent 不存在: %s", task.AgentID)
		}
	}

	// 保留一些不应由用户更新的字段
	task.Status = existingTask.Status
	task.LastError = existingTask.LastError
	task.LastCollectTime = existingTask.LastCollectTime

	// 保存任务
	if err := s.taskRepo.UpdateTask(task); err != nil {
		return fmt.Errorf("更新任务失败: %w", err)
	}

	s.logger.Info("更新采集任务成功", zap.String("taskName", task.TaskName), zap.String("taskID", task.TaskID))

	// 如果启用状态发生变化或其他配置发生变化，通过 AgentService 发送更新
	if s.agentService != nil {
		if err := s.agentService.UpdateTaskConfiguration(task); err != nil {
			s.logger.Error("发送任务配置失败", zap.String("taskID", task.TaskID), zap.Error(err))
		}
	}

	return nil
}

// DeleteTask 删除采集任务
func (s *CollectorTaskService) DeleteTask(taskID string) error {
	// 检查任务是否存在
	task, err := s.taskRepo.GetTaskByTaskID(taskID)
	if err != nil {
		return fmt.Errorf("查询任务失败: %w", err)
	}
	if task == nil {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	// 先禁用任务
	if task.IsEnabled && s.agentService != nil {
		task.IsEnabled = false
		if err := s.agentService.UpdateTaskConfiguration(task); err != nil {
			s.logger.Error("禁用任务失败", zap.String("taskID", taskID), zap.Error(err))
		}
	}

	// 删除任务
	if err := s.taskRepo.DeleteTask(taskID); err != nil {
		return fmt.Errorf("删除任务失败: %w", err)
	}

	s.logger.Info("删除采集任务成功", zap.String("taskID", taskID))
	return nil
}

// GetTask 获取采集任务
func (s *CollectorTaskService) GetTask(taskID string) (*model.CollectorTask, error) {
	task, err := s.taskRepo.GetTaskByTaskID(taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务失败: %w", err)
	}
	if task == nil {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}
	return task, nil
}

// GetAllTasks 获取所有采集任务
func (s *CollectorTaskService) GetAllTasks() ([]*model.CollectorTask, error) {
	tasks, err := s.taskRepo.GetAllTasks()
	if err != nil {
		return nil, fmt.Errorf("获取所有任务失败: %w", err)
	}
	return tasks, nil
}

// GetDeviceTasks 获取设备的采集任务
func (s *CollectorTaskService) GetDeviceTasks(deviceID uint) ([]*model.CollectorTask, error) {
	tasks, err := s.taskRepo.GetTasksByDeviceID(deviceID)
	if err != nil {
		return nil, fmt.Errorf("获取设备任务失败: %w", err)
	}
	return tasks, nil
}

// GetAgentTasks 获取 Agent 的采集任务
func (s *CollectorTaskService) GetAgentTasks(agentID string) ([]*model.CollectorTask, error) {
	tasks, err := s.taskRepo.GetTasksByAgentID(agentID)
	if err != nil {
		return nil, fmt.Errorf("获取 Agent 任务失败: %w", err)
	}
	return tasks, nil
}

// EnableTask 启用采集任务
func (s *CollectorTaskService) EnableTask(taskID string) error {
	task, err := s.taskRepo.GetTaskByTaskID(taskID)
	if err != nil {
		return fmt.Errorf("查询任务失败: %w", err)
	}
	if task == nil {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	if task.IsEnabled {
		s.logger.Info("任务已经是启用状态", zap.String("taskID", taskID))
		return nil
	}

	task.IsEnabled = true
	if err := s.taskRepo.UpdateTask(task); err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	// 通过 AgentService 发送更新
	if s.agentService != nil {
		if err := s.agentService.UpdateTaskConfiguration(task); err != nil {
			s.logger.Error("发送任务配置失败", zap.String("taskID", taskID), zap.Error(err))
		}
	}

	s.logger.Info("启用采集任务成功", zap.String("taskID", taskID))
	return nil
}

// DisableTask 禁用采集任务
func (s *CollectorTaskService) DisableTask(taskID string) error {
	task, err := s.taskRepo.GetTaskByTaskID(taskID)
	if err != nil {
		return fmt.Errorf("查询任务失败: %w", err)
	}
	if task == nil {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	if !task.IsEnabled {
		s.logger.Info("任务已经是禁用状态", zap.String("taskID", taskID))
		return nil
	}

	task.IsEnabled = false
	if err := s.taskRepo.UpdateTask(task); err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	// 通过 AgentService 发送更新
	if s.agentService != nil {
		if err := s.agentService.UpdateTaskConfiguration(task); err != nil {
			s.logger.Error("发送任务配置失败", zap.String("taskID", taskID), zap.Error(err))
		}
	}

	s.logger.Info("禁用采集任务成功", zap.String("taskID", taskID))
	return nil
}
