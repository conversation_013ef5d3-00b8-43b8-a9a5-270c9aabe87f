package service

import (
	"fmt"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"

	"go.uber.org/zap"
)

// SupportedMetricService 支持指标服务接口
type SupportedMetricService interface {
	CreateSupportedMetric(metric *model.SupportedMetric) error
	GetSupportedMetricByKey(metricKey string) (*model.SupportedMetric, error)
	GetSupportedMetricByID(id uint) (*model.SupportedMetric, error)
	UpdateSupportedMetric(metric *model.SupportedMetric) error
	DeleteSupportedMetric(id uint) error
	GetAllSupportedMetrics() ([]*model.SupportedMetric, error)
	GetSupportedMetricsByCollectorType(collectorType string) ([]*model.SupportedMetric, error)
	GetActiveSupportedMetrics() ([]*model.SupportedMetric, error)
	SeedDefaultMetrics() error
}

// supportedMetricServiceImpl 支持指标服务实现
type supportedMetricServiceImpl struct {
	repo   repository.SupportedMetricRepository
	logger *zap.Logger
}

// NewSupportedMetricService 创建支持指标服务实例
func NewSupportedMetricService(repo repository.SupportedMetricRepository, logger *zap.Logger) SupportedMetricService {
	return &supportedMetricServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

// CreateSupportedMetric 创建支持指标
func (s *supportedMetricServiceImpl) CreateSupportedMetric(metric *model.SupportedMetric) error {
	// 验证指标键是否已存在
	existing, err := s.repo.GetSupportedMetricByKey(metric.MetricKey)
	if err != nil {
		s.logger.Error("检查指标键是否存在失败", zap.Error(err))
		return fmt.Errorf("检查指标键是否存在失败: %w", err)
	}
	if existing != nil {
		return fmt.Errorf("指标键 %s 已存在", metric.MetricKey)
	}

	err = s.repo.CreateSupportedMetric(metric)
	if err != nil {
		s.logger.Error("创建支持指标失败", zap.Error(err))
		return err
	}

	s.logger.Info("成功创建支持指标", zap.String("metric_key", metric.MetricKey))
	return nil
}

// GetSupportedMetricByKey 根据指标键获取支持指标
func (s *supportedMetricServiceImpl) GetSupportedMetricByKey(metricKey string) (*model.SupportedMetric, error) {
	return s.repo.GetSupportedMetricByKey(metricKey)
}

// GetSupportedMetricByID 根据ID获取支持指标
func (s *supportedMetricServiceImpl) GetSupportedMetricByID(id uint) (*model.SupportedMetric, error) {
	return s.repo.GetSupportedMetricByID(id)
}

// UpdateSupportedMetric 更新支持指标
func (s *supportedMetricServiceImpl) UpdateSupportedMetric(metric *model.SupportedMetric) error {
	err := s.repo.UpdateSupportedMetric(metric)
	if err != nil {
		s.logger.Error("更新支持指标失败", zap.Error(err))
		return err
	}

	s.logger.Info("成功更新支持指标", zap.String("metric_key", metric.MetricKey))
	return nil
}

// DeleteSupportedMetric 删除支持指标
func (s *supportedMetricServiceImpl) DeleteSupportedMetric(id uint) error {
	err := s.repo.DeleteSupportedMetric(id)
	if err != nil {
		s.logger.Error("删除支持指标失败", zap.Error(err))
		return err
	}

	s.logger.Info("成功删除支持指标", zap.Uint("id", id))
	return nil
}

// GetAllSupportedMetrics 获取所有支持指标
func (s *supportedMetricServiceImpl) GetAllSupportedMetrics() ([]*model.SupportedMetric, error) {
	return s.repo.GetAllSupportedMetrics()
}

// GetSupportedMetricsByCollectorType 根据采集器类型获取支持指标
func (s *supportedMetricServiceImpl) GetSupportedMetricsByCollectorType(collectorType string) ([]*model.SupportedMetric, error) {
	return s.repo.GetSupportedMetricsByCollectorType(collectorType)
}

// GetActiveSupportedMetrics 获取所有激活的支持指标
func (s *supportedMetricServiceImpl) GetActiveSupportedMetrics() ([]*model.SupportedMetric, error) {
	return s.repo.GetActiveSupportedMetrics()
}

// SeedDefaultMetrics 初始化默认支持指标
func (s *supportedMetricServiceImpl) SeedDefaultMetrics() error {
	defaultMetrics := []*model.SupportedMetric{
		{
			MetricKey:     "mysql.connections.active",
			MetricName:    "MySQL活跃连接数",
			Description:   "当前MySQL数据库的活跃连接数量",
			DataType:      "numeric",
			Unit:          "count",
			Metadata:      `{"category": "database", "severity": "normal"}`,
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.queries.per_second",
			MetricName:    "MySQL每秒查询数",
			Description:   "MySQL数据库每秒执行的查询数量",
			DataType:      "numeric",
			Unit:          "qps",
			Metadata:      `{"category": "database", "severity": "normal"}`,
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.innodb.buffer_pool_hit_ratio",
			MetricName:    "MySQL InnoDB缓冲池命中率",
			Description:   "InnoDB缓冲池的命中率百分比",
			DataType:      "numeric",
			Unit:          "percent",
			Metadata:      `{"category": "database", "severity": "high"}`,
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "redis.memory.used",
			MetricName:    "Redis已使用内存",
			Description:   "Redis服务器当前使用的内存大小",
			DataType:      "numeric",
			Unit:          "bytes",
			Metadata:      `{"category": "cache", "severity": "normal"}`,
			IsActive:      true,
			CollectorType: "redis",
		},
		{
			MetricKey:     "redis.clients.connected",
			MetricName:    "Redis连接客户端数",
			Description:   "当前连接到Redis服务器的客户端数量",
			DataType:      "numeric",
			Unit:          "count",
			Metadata:      `{"category": "cache", "severity": "normal"}`,
			IsActive:      true,
			CollectorType: "redis",
		},
		{
			MetricKey:     "system.cpu.utilization",
			MetricName:    "系统CPU使用率",
			Description:   "系统整体CPU使用率百分比",
			DataType:      "numeric",
			Unit:          "percent",
			Metadata:      `{"category": "system", "severity": "high"}`,
			IsActive:      true,
			CollectorType: "system",
		},
		{
			MetricKey:     "system.memory.utilization",
			MetricName:    "系统内存使用率",
			Description:   "系统内存使用率百分比",
			DataType:      "numeric",
			Unit:          "percent",
			Metadata:      `{"category": "system", "severity": "high"}`,
			IsActive:      true,
			CollectorType: "system",
		},
		{
			MetricKey:     "application.status",
			MetricName:    "应用程序状态",
			Description:   "应用程序运行状态信息",
			DataType:      "string",
			Unit:          "",
			Metadata:      `{"category": "application", "severity": "critical"}`,
			IsActive:      true,
			CollectorType: "application",
		},
		{
			MetricKey:     "network.latency.complex",
			MetricName:    "复杂网络延迟统计",
			Description:   "包含多维度网络延迟统计信息的JSON数据",
			DataType:      "json",
			Unit:          "ms",
			Metadata:      `{"category": "network", "severity": "normal", "dimensions": ["source", "destination", "protocol"]}`,
			IsActive:      true,
			CollectorType: "network",
		},
	}

	for _, metric := range defaultMetrics {
		existing, err := s.repo.GetSupportedMetricByKey(metric.MetricKey)
		if err != nil {
			s.logger.Error("检查默认指标是否存在失败", zap.Error(err))
			continue
		}
		if existing != nil {
			continue // 已存在，跳过
		}

		err = s.repo.CreateSupportedMetric(metric)
		if err != nil {
			s.logger.Error("创建默认支持指标失败",
				zap.String("metric_key", metric.MetricKey),
				zap.Error(err))
			continue
		}
		s.logger.Info("成功创建默认支持指标", zap.String("metric_key", metric.MetricKey))
	}

	return nil
}
