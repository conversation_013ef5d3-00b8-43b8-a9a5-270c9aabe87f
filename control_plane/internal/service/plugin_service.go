package service

import (
	"context"
	"fmt"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"
	pb "aiops/pkg/proto"
	plugininterface "aiops/plugins/interface"
	"aiops/plugins/loader"

	"go.uber.org/zap"
)

// PluginService 插件服务
type PluginService struct {
	pluginManager *loader.PluginManager
	agentRepo     repository.AgentRepository
	deviceRepo    repository.DeviceRepository
	logger        *zap.Logger
}

// NewPluginService 创建新的插件服务
func NewPluginService(
	pluginManager *loader.PluginManager,
	agentRepo repository.AgentRepository,
	deviceRepo repository.DeviceRepository,
	logger *zap.Logger,
) *PluginService {
	return &PluginService{
		pluginManager: pluginManager,
		agentRepo:     agentRepo,
		deviceRepo:    deviceRepo,
		logger:        logger,
	}
}

// GetAvailableCollectors 获取可用的采集器插件
func (s *PluginService) GetAvailableCollectors() []plugininterface.Collector {
	return s.pluginManager.GetCollectors()
}

// GetSupportedMetrics 获取设备类型支持的指标
func (s *PluginService) GetSupportedMetrics(deviceType string) ([]*pb.SupportedMetric, error) {
	collectors := s.GetAvailableCollectors()

	var allMetrics []*pb.SupportedMetric
	for _, collector := range collectors {
		// 检查是否为增强型采集器
		if enhancedCollector, ok := collector.(plugininterface.EnhancedCollector); ok {
			supportedTypes := enhancedCollector.GetSupportedDeviceTypes()

			// 检查该采集器是否支持指定的设备类型
			for _, supportedType := range supportedTypes {
				if supportedType == deviceType {
					metrics, err := enhancedCollector.GetSupportedMetrics(deviceType)
					if err != nil {
						s.logger.Warn("Failed to get supported metrics from collector",
							zap.String("collector", collector.GetInfo().Name),
							zap.String("deviceType", deviceType),
							zap.Error(err))
						continue
					}
					allMetrics = append(allMetrics, metrics...)
					break
				}
			}
		}
	}

	return allMetrics, nil
}

// CreateCollectorTask 创建采集任务
func (s *PluginService) CreateCollectorTask(ctx context.Context, taskConfig *pb.CollectorTaskConfig) error {
	// 查找合适的采集器插件
	collector, err := s.findCollectorForDevice(taskConfig.DeviceType)
	if err != nil {
		return fmt.Errorf("no suitable collector found for device type %s: %w", taskConfig.DeviceType, err)
	}

	// 验证配置
	if err := collector.ValidateConfig(taskConfig); err != nil {
		return fmt.Errorf("invalid task configuration: %w", err)
	}

	// 启动采集 - 检查是否为增强型采集器
	if enhancedCollector, ok := collector.(plugininterface.EnhancedCollector); ok {
		if err := enhancedCollector.StartContinuousCollection(ctx, taskConfig.TaskId); err != nil {
			return fmt.Errorf("failed to start collection: %w", err)
		}
	} else {
		return fmt.Errorf("collector does not support continuous collection operation")
	}

	s.logger.Info("Collector task created successfully",
		zap.String("taskId", taskConfig.TaskId),
		zap.String("deviceType", taskConfig.DeviceType),
		zap.String("collector", collector.GetInfo().Name))

	return nil
}

// StopCollectorTask 停止采集任务
func (s *PluginService) StopCollectorTask(ctx context.Context, taskID string, deviceType string) error {
	// 查找合适的采集器插件
	collector, err := s.findCollectorForDevice(deviceType)
	if err != nil {
		return fmt.Errorf("no suitable collector found for device type %s: %w", deviceType, err)
	}

	// 停止采集 - 检查是否为增强型采集器
	if enhancedCollector, ok := collector.(plugininterface.EnhancedCollector); ok {
		if err := enhancedCollector.StopContinuousCollection(ctx, taskID); err != nil {
			return fmt.Errorf("failed to stop collection: %w", err)
		}
	} else {
		return fmt.Errorf("collector does not support continuous collection operation")
	}

	s.logger.Info("Collector task stopped successfully",
		zap.String("taskId", taskID),
		zap.String("deviceType", deviceType),
		zap.String("collector", collector.GetInfo().Name))

	return nil
}

// CollectMetrics 使用插件采集指标
func (s *PluginService) CollectMetrics(ctx context.Context, taskConfig *pb.CollectorTaskConfig) ([]*pb.MetricData, error) {
	// 查找合适的采集器插件
	collector, err := s.findCollectorForDevice(taskConfig.DeviceType)
	if err != nil {
		return nil, fmt.Errorf("no suitable collector found for device type %s: %w", taskConfig.DeviceType, err)
	}

	// 执行采集
	metrics, err := collector.CollectMetrics(ctx, taskConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to collect metrics: %w", err)
	}

	return metrics, nil
}

// ProcessMetrics 使用插件处理指标数据
func (s *PluginService) ProcessMetrics(ctx context.Context, metrics []*pb.MetricData) ([]*pb.MetricData, error) {
	processors := s.pluginManager.GetProcessors()

	processedMetrics := metrics
	for _, processor := range processors {
		var err error
		processedMetrics, err = processor.ProcessMetrics(ctx, processedMetrics)
		if err != nil {
			s.logger.Warn("Failed to process metrics with processor",
				zap.String("processor", processor.GetInfo().Name),
				zap.Error(err))
			continue
		}
	}

	return processedMetrics, nil
}

// SendAlert 使用插件发送告警
func (s *PluginService) SendAlert(ctx context.Context, alert *model.AlertEvent) error {
	alerters := s.pluginManager.GetAlerters()

	if len(alerters) == 0 {
		return fmt.Errorf("no alerter plugins available")
	}

	// 转换 model.AlertEvent 到 plugininterface.AlertEvent
	pluginAlert := s.convertToPluginAlert(alert)

	var lastErr error
	sentCount := 0

	for _, alerter := range alerters {
		if err := alerter.SendAlert(ctx, pluginAlert); err != nil {
			s.logger.Warn("Failed to send alert with alerter",
				zap.String("alerter", alerter.GetInfo().Name),
				zap.Error(err))
			lastErr = err
			continue
		}
		sentCount++
	}

	if sentCount == 0 {
		return fmt.Errorf("failed to send alert with any alerter: %w", lastErr)
	}

	s.logger.Info("Alert sent successfully",
		zap.String("alertId", fmt.Sprintf("%d", alert.ID)),
		zap.Int("senderCount", sentCount))

	return nil
}

// AnalyzeMetrics 使用插件分析指标数据
func (s *PluginService) AnalyzeMetrics(ctx context.Context, metrics []*pb.MetricData) ([]*plugininterface.AnalysisResult, error) {
	analyzers := s.pluginManager.GetAnalyzers()

	var results []*plugininterface.AnalysisResult
	for _, analyzer := range analyzers {
		result, err := analyzer.AnalyzeMetrics(ctx, metrics)
		if err != nil {
			s.logger.Warn("Failed to analyze metrics with analyzer",
				zap.String("analyzer", analyzer.GetInfo().Name),
				zap.Error(err))
			continue
		}
		results = append(results, result)
	}

	return results, nil
}

// DetectAnomalies 使用插件检测异常
func (s *PluginService) DetectAnomalies(ctx context.Context, metrics []*pb.MetricData) ([]*plugininterface.Anomaly, error) {
	analyzers := s.pluginManager.GetAnalyzers()

	var allAnomalies []*plugininterface.Anomaly
	for _, analyzer := range analyzers {
		anomalies, err := analyzer.DetectAnomalies(ctx, metrics)
		if err != nil {
			s.logger.Warn("Failed to detect anomalies with analyzer",
				zap.String("analyzer", analyzer.GetInfo().Name),
				zap.Error(err))
			continue
		}
		allAnomalies = append(allAnomalies, anomalies...)
	}

	return allAnomalies, nil
}

// GetPluginInfo 获取插件信息
func (s *PluginService) GetPluginInfo(name, version string) (*plugininterface.PluginInfo, error) {
	plugin, err := s.pluginManager.GetRegistry().Get(name, version)
	if err != nil {
		return nil, err
	}

	return plugin.GetInfo(), nil
}

// ListPlugins 列出所有插件
func (s *PluginService) ListPlugins() []*plugininterface.PluginInfo {
	registeredPlugins := s.pluginManager.GetRegistry().List()

	var pluginInfos []*plugininterface.PluginInfo
	for _, registered := range registeredPlugins {
		pluginInfos = append(pluginInfos, registered.Info)
	}

	return pluginInfos
}

// GetPluginMetrics 获取插件指标
func (s *PluginService) GetPluginMetrics(ctx context.Context, name, version string) (map[string]interface{}, error) {
	return s.pluginManager.GetRegistry().GetMetrics(name, version)
}

// HealthCheckPlugins 检查所有插件健康状态
func (s *PluginService) HealthCheckPlugins(ctx context.Context) map[string]error {
	registeredPlugins := s.pluginManager.GetRegistry().List()

	healthStatus := make(map[string]error)
	for _, registered := range registeredPlugins {
		pluginKey := fmt.Sprintf("%s:%s", registered.Info.Name, registered.Info.Version)
		err := s.pluginManager.GetRegistry().Health(registered.Info.Name, registered.Info.Version)
		healthStatus[pluginKey] = err
	}

	return healthStatus
}

// convertToPluginAlert 将 model.AlertEvent 转换为 plugininterface.AlertEvent
func (s *PluginService) convertToPluginAlert(alert *model.AlertEvent) *plugininterface.AlertEvent {
	// 获取设备信息
	deviceName := s.getDeviceName(alert.DeviceID)

	return &plugininterface.AlertEvent{
		ID:             fmt.Sprintf("%d", alert.ID),
		Title:          s.generateAlertTitle(alert),
		Description:    alert.Message,
		Level:          s.convertSeverityToLevel(alert.Severity),
		DeviceID:       fmt.Sprintf("%d", alert.DeviceID),
		DeviceName:     deviceName,
		MetricKey:      alert.MetricKey,
		CurrentValue:   alert.TriggerValue,
		ThresholdValue: s.getThresholdValue(alert.RuleID),
		Timestamp:      alert.TriggerTime,
	}
}

// getDeviceName 获取设备名称
func (s *PluginService) getDeviceName(deviceID uint) string {
	if s.deviceRepo != nil {
		if device, err := s.deviceRepo.GetDeviceByID(deviceID); err == nil && device != nil {
			return device.Name
		}
	}
	return fmt.Sprintf("Device-%d", deviceID)
}

// generateAlertTitle 生成告警标题
func (s *PluginService) generateAlertTitle(alert *model.AlertEvent) string {
	return fmt.Sprintf("%s Alert: %s", alert.Severity, alert.MetricKey)
}

// convertSeverityToLevel 转换严重程度到告警级别
func (s *PluginService) convertSeverityToLevel(severity string) string {
	switch severity {
	case "critical":
		return "critical"
	case "warning":
		return "warning"
	case "info":
		return "info"
	default:
		return "error"
	}
}

// getThresholdValue 获取阈值（简化实现）
func (s *PluginService) getThresholdValue(ruleID uint) float64 {
	// 这里应该从数据库查询告警规则获取阈值
	// 为了简化，返回一个默认值
	return 0.0
}

// findCollectorForDevice 查找适合指定设备类型的采集器
func (s *PluginService) findCollectorForDevice(deviceType string) (plugininterface.Collector, error) {
	collectors := s.GetAvailableCollectors()

	for _, collector := range collectors {
		// 检查是否为增强型采集器
		if enhancedCollector, ok := collector.(plugininterface.EnhancedCollector); ok {
			supportedTypes := enhancedCollector.GetSupportedDeviceTypes()
			for _, supportedType := range supportedTypes {
				if supportedType == deviceType {
					return collector, nil
				}
			}
		}
	}

	return nil, fmt.Errorf("no collector found for device type %s", deviceType)
}
