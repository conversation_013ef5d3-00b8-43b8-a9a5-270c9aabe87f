package service

import (
	"fmt"

	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"
	"aiops/pkg/log"
)

// UserService 提供用户相关的业务逻辑
type UserService struct {
	userRepo repository.UserRepository
	logger   *log.Logger
}

// NewUserService 创建一个新的 UserService 实例
func NewUserService(
	userRepo repository.UserRepository,
	logger *log.Logger,
) *UserService {
	return &UserService{
		userRepo: userRepo,
		logger:   logger,
	}
}

// CreateUser 创建用户
func (s *UserService) CreateUser(user *model.User) error {
	// 检查用户名是否已存在
	existingUser, err := s.userRepo.GetUserByUsername(user.Username)
	if err != nil {
		return fmt.Errorf("查询用户失败: %w", err)
	}
	if existingUser != nil {
		return fmt.Errorf("用户名已存在: %s", user.Username)
	}

	// 哈希密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.PasswordHash), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}
	user.PasswordHash = string(hashedPassword)

	// 设置默认角色
	if user.Role == "" {
		user.Role = "viewer" // 默认为只读用户
	}

	// 保存用户
	if err := s.userRepo.CreateUser(user); err != nil {
		return fmt.Errorf("创建用户失败: %w", err)
	}

	s.logger.Info("创建用户成功", zap.String("username", user.Username), zap.Uint("id", user.ID))
	return nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(user *model.User) error {
	// 检查用户是否存在
	existingUser, err := s.userRepo.GetUserByID(user.ID)
	if err != nil {
		return fmt.Errorf("查询用户失败: %w", err)
	}
	if existingUser == nil {
		return fmt.Errorf("用户不存在: %d", user.ID)
	}

	// 如果用户名发生变化，检查新用户名是否已存在
	if user.Username != existingUser.Username {
		userWithSameName, err := s.userRepo.GetUserByUsername(user.Username)
		if err != nil {
			return fmt.Errorf("查询用户失败: %w", err)
		}
		if userWithSameName != nil && userWithSameName.ID != user.ID {
			return fmt.Errorf("用户名已被占用: %s", user.Username)
		}
	}

	// 如果提供了新密码，需要哈希处理
	// 这里我们假设 PasswordHash 字段存储的是明文密码（在真实应用中不应该这样）
	if user.PasswordHash != "" && user.PasswordHash != existingUser.PasswordHash {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.PasswordHash), bcrypt.DefaultCost)
		if err != nil {
			return fmt.Errorf("密码加密失败: %w", err)
		}
		user.PasswordHash = string(hashedPassword)
	} else {
		// 保留原密码
		user.PasswordHash = existingUser.PasswordHash
	}

	// 保存用户
	if err := s.userRepo.UpdateUser(user); err != nil {
		return fmt.Errorf("更新用户失败: %w", err)
	}

	s.logger.Info("更新用户成功", zap.String("username", user.Username), zap.Uint("id", user.ID))
	return nil
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(id uint) error {
	// 检查用户是否存在
	user, err := s.userRepo.GetUserByID(id)
	if err != nil {
		return fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在: %d", id)
	}

	// 删除用户
	if err := s.userRepo.DeleteUser(id); err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}

	s.logger.Info("删除用户成功", zap.String("username", user.Username), zap.Uint("id", id))
	return nil
}

// GetUser 获取用户信息
func (s *UserService) GetUser(id uint) (*model.User, error) {
	user, err := s.userRepo.GetUserByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在: %d", id)
	}

	// 清除密码信息
	user.PasswordHash = ""

	return user, nil
}

// GetAllUsers 获取所有用户
func (s *UserService) GetAllUsers() ([]*model.User, error) {
	users, err := s.userRepo.GetAllUsers()
	if err != nil {
		return nil, fmt.Errorf("获取所有用户失败: %w", err)
	}

	// 清除密码信息
	for _, user := range users {
		user.PasswordHash = ""
	}

	return users, nil
}

// Authenticate 验证用户身份
func (s *UserService) Authenticate(username, password string) (*model.User, bool, error) {
	// 查询用户
	user, err := s.userRepo.GetUserByUsername(username)
	if err != nil {
		return nil, false, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, false, nil
	}

	// 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password))
	if err != nil {
		return user, false, nil
	}

	// 认证成功，返回用户信息（不含密码）
	authenticatedUser := &model.User{
		Model:    user.Model,
		Username: user.Username,
		Role:     user.Role,
	}

	return authenticatedUser, true, nil
}

// ChangePassword 修改用户密码
func (s *UserService) ChangePassword(userID uint, oldPassword, newPassword string) error {
	// 查询用户
	user, err := s.userRepo.GetUserByID(userID)
	if err != nil {
		return fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在: %d", userID)
	}

	// 验证旧密码
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(oldPassword))
	if err != nil {
		return fmt.Errorf("旧密码不正确")
	}

	// 哈希新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}

	// 更新密码
	user.PasswordHash = string(hashedPassword)
	if err := s.userRepo.UpdateUser(user); err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	s.logger.Info("用户修改密码成功", zap.String("username", user.Username), zap.Uint("id", userID))
	return nil
}

// InitAdminUser 初始化管理员用户
func (s *UserService) InitAdminUser(username, password string) error {
	// 检查是否已存在管理员用户
	users, err := s.userRepo.GetAllUsers()
	if err != nil {
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 检查是否已存在管理员
	for _, user := range users {
		if user.Role == "admin" {
			s.logger.Info("管理员用户已存在，跳过初始化")
			return nil
		}
	}

	// 创建管理员用户
	adminUser := &model.User{
		Username:     username,
		PasswordHash: password,
		Role:         "admin",
	}

	if err := s.CreateUser(adminUser); err != nil {
		return fmt.Errorf("创建管理员用户失败: %w", err)
	}

	s.logger.Info("初始化管理员用户成功", zap.String("username", username))
	return nil
}
