package service

import (
	"time"

	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"

	"go.uber.org/zap"
)

// LogService 日志服务接口
type LogService interface {
	SaveLogs(logs []*model.LogEntry) error
	GetLogsByDeviceID(deviceID string, startTime, endTime time.Time, logLevel string, limit int) ([]*model.LogEntry, error)
	GetLogsByTimeRange(startTime, endTime time.Time, logLevel string, limit int) ([]*model.LogEntry, error)
	GetLogsBySource(source string, startTime, endTime time.Time, limit int) ([]*model.LogEntry, error)
	CleanupOldLogs(retentionDays int) error
}

// logServiceImpl 日志服务实现
type logServiceImpl struct {
	repo   repository.LogRepository
	logger *zap.Logger
}

// NewLogService 创建日志服务实例
func NewLogService(repo repository.LogRepository, logger *zap.Logger) LogService {
	return &logServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

// SaveLogs 保存日志数据
func (s *logServiceImpl) SaveLogs(logs []*model.LogEntry) error {
	if len(logs) == 0 {
		return nil
	}

	err := s.repo.SaveLogs(logs)
	if err != nil {
		s.logger.Error("保存日志数据失败", zap.Error(err), zap.Int("count", len(logs)))
		return err
	}

	s.logger.Debug("成功保存日志数据", zap.Int("count", len(logs)))
	return nil
}

// GetLogsByDeviceID 根据设备ID获取日志
func (s *logServiceImpl) GetLogsByDeviceID(deviceID string, startTime, endTime time.Time, logLevel string, limit int) ([]*model.LogEntry, error) {
	logs, err := s.repo.GetLogsByDeviceID(deviceID, startTime, endTime, logLevel, limit)
	if err != nil {
		s.logger.Error("根据设备ID获取日志失败",
			zap.String("device_id", deviceID),
			zap.String("log_level", logLevel),
			zap.Error(err))
		return nil, err
	}

	return logs, nil
}

// GetLogsByTimeRange 根据时间范围获取日志
func (s *logServiceImpl) GetLogsByTimeRange(startTime, endTime time.Time, logLevel string, limit int) ([]*model.LogEntry, error) {
	logs, err := s.repo.GetLogsByTimeRange(startTime, endTime, logLevel, limit)
	if err != nil {
		s.logger.Error("根据时间范围获取日志失败",
			zap.String("log_level", logLevel),
			zap.Error(err))
		return nil, err
	}

	return logs, nil
}

// GetLogsBySource 根据日志来源获取日志
func (s *logServiceImpl) GetLogsBySource(source string, startTime, endTime time.Time, limit int) ([]*model.LogEntry, error) {
	logs, err := s.repo.GetLogsBySource(source, startTime, endTime, limit)
	if err != nil {
		s.logger.Error("根据来源获取日志失败",
			zap.String("source", source),
			zap.Error(err))
		return nil, err
	}

	return logs, nil
}

// CleanupOldLogs 清理过期日志
func (s *logServiceImpl) CleanupOldLogs(retentionDays int) error {
	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)

	err := s.repo.DeleteLogsBeforeTime(cutoffTime)
	if err != nil {
		s.logger.Error("清理过期日志失败",
			zap.Int("retention_days", retentionDays),
			zap.Error(err))
		return err
	}

	s.logger.Info("成功清理过期日志",
		zap.Int("retention_days", retentionDays),
		zap.Time("cutoff_time", cutoffTime))
	return nil
}
