package grpc

import (
	"encoding/json"

	"aiops/control_plane/internal/model"
	pb "aiops/pkg/proto"
)

// convertPbMetricDataToModel 将 protobuf 的 MetricData 转换为内部模型
func convertPbMetricDataToModel(pbMetric *pb.MetricData) *model.MetricData {
	// 将标签转换为 JSON 字符串
	labels := "{}"
	if len(pbMetric.Labels) > 0 {
		labelsBytes, _ := json.Marshal(pbMetric.Labels)
		labels = string(labelsBytes)
	}

	metric := &model.MetricData{
		DeviceID:  pbMetric.DeviceId,
		MetricKey: pbMetric.MetricKey,
		Timestamp: pbMetric.Timestamp,
		Labels:    labels,
	}

	// 根据 protobuf 中的 oneof 值类型设置相应的字段
	switch value := pbMetric.ValueType.(type) {
	case *pb.MetricData_NumericValue:
		metric.NumericValue = &value.NumericValue
	case *pb.MetricData_StringValue:
		metric.StringValue = &value.StringValue
	case *pb.MetricData_BooleanValue:
		metric.BooleanValue = &value.BooleanValue
	}

	// 如果有 JSON 数据，设置 JSONData 字段
	if pbMetric.JsonData != "" {
		metric.JSONData = pbMetric.JsonData
	}

	return metric
}
