package grpc

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"sync"
	"time"

	"aiops/control_plane/config"
	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/reflection"
	"google.golang.org/grpc/status"
)

// Server 是 gRPC 服务器的实现
type Server struct {
	pb.UnimplementedAgentServiceServer
	config                 *config.Config
	agentService           *service.AgentService
	metricService          *service.MetricService
	logService             service.LogService
	supportedMetricService service.SupportedMetricService
	logger                 *log.Logger
	grpcServer             *grpc.Server
}

// NewServer 创建一个新的 gRPC 服务器
func NewServer(
	config *config.Config,
	agentService *service.AgentService,
	metricService *service.MetricService,
	logService service.LogService,
	supportedMetricService service.SupportedMetricService,
	logger *log.Logger,
) *Server {
	return &Server{
		config:                 config,
		agentService:           agentService,
		metricService:          metricService,
		logService:             logService,
		supportedMetricService: supportedMetricService,
		logger:                 logger,
	}
}

// Start 启动 gRPC 服务器
func (s *Server) Start() error {
	// 创建监听器
	addr := fmt.Sprintf("0.0.0.0:%d", s.config.GRPCPort)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to listen: %v", err)
	}

	// 创建 gRPC 服务器
	s.grpcServer = grpc.NewServer()

	// 注册服务
	pb.RegisterAgentServiceServer(s.grpcServer, s)

	// 注册反射服务，方便测试
	reflection.Register(s.grpcServer)

	// 启动服务
	s.logger.Info("启动 gRPC 服务器", zap.String("监听地址", addr))
	go func() {
		if err := s.grpcServer.Serve(lis); err != nil {
			s.logger.Error("gRPC 服务器异常退出", zap.Error(err))
		}
	}()

	return nil
}

// Stop 停止 gRPC 服务器
func (s *Server) Stop() {
	if s.grpcServer != nil {
		s.logger.Info("正在停止 gRPC 服务器")
		s.grpcServer.GracefulStop()
	}
}

// RegisterAgent 实现 gRPC RegisterAgent 方法
func (s *Server) RegisterAgent(ctx context.Context, req *pb.RegisterAgentRequest) (*pb.RegisterAgentResponse, error) {
	s.logger.Info("接收到 Agent 注册请求", zap.String("agentID", req.AgentId), zap.String("IP", req.AgentIp))

	success, err := s.agentService.RegisterAgent(ctx, req.AgentId, req.AgentIp, req.SupportedCollectorTypes)
	if err != nil {
		s.logger.Error("Agent 注册失败", zap.Error(err))
		return &pb.RegisterAgentResponse{
			Success: false,
			Message: fmt.Sprintf("注册失败: %v", err),
		}, status.Error(codes.Internal, err.Error())
	}

	return &pb.RegisterAgentResponse{
		Success: success,
		Message: "注册成功",
	}, nil
}

// StreamCollectorTasks 实现 AgentService 的 StreamCollectorTasks 方法
func (s *Server) StreamCollectorTasks(stream pb.AgentService_StreamCollectorTasksServer) error {
	// 等待第一条消息以获取 AgentID
	taskStatus, err := stream.Recv()
	if err != nil {
		s.logger.Error("接收首条消息失败", zap.Error(err))
		return status.Errorf(codes.Internal, "接收首条消息失败: %v", err)
	}

	agentID := getAgentIDFromTaskStatus(taskStatus)
	s.logger.Info("Agent 连接到任务流", zap.String("agentID", agentID))

	// 注册 Agent 的流通道
	agentStream, err := s.agentService.RegisterAgentStream(agentID)
	if err != nil {
		s.logger.Error("注册 Agent 流失败", zap.String("agentID", agentID), zap.Error(err))
		return status.Errorf(codes.Internal, "注册 Agent 流失败: %v", err)
	}

	// 处理首条消息
	err = s.processTaskStatus(agentID, convertPbTaskStatusToModel(taskStatus))
	if err != nil {
		s.logger.Error("处理任务状态失败", zap.Error(err))
	}

	// 创建 WaitGroup 来等待两个 goroutine 完成
	var wg sync.WaitGroup
	wg.Add(2)

	// 错误通道，用于传递错误
	errCh := make(chan error, 2)

	// 从 Agent 接收任务状态
	go func() {
		defer wg.Done()

		for {
			taskStatus, err := stream.Recv()
			if err == io.EOF {
				s.logger.Info("Agent 关闭了任务状态流", zap.String("agentID", agentID))
				return
			}
			if err != nil {
				s.logger.Error("接收任务状态失败", zap.String("agentID", agentID), zap.Error(err))
				errCh <- err
				return
			}

			// 处理任务状态
			err = s.processTaskStatus(agentID, convertPbTaskStatusToModel(taskStatus))
			if err != nil {
				s.logger.Error("处理任务状态失败", zap.Error(err))
			}
		}
	}()

	// 向 Agent 发送任务配置
	go func() {
		defer wg.Done()

		for {
			select {
			case taskConfig, ok := <-agentStream.TaskConfigCh:
				if !ok {
					s.logger.Info("任务配置通道已关闭", zap.String("agentID", agentID))
					return
				}

				// 转换为 protobuf 格式
				pbTaskConfig := convertModelTaskConfigToPb(taskConfig)

				// 发送任务配置
				if err := stream.Send(pbTaskConfig); err != nil {
					s.logger.Error("发送任务配置失败", zap.String("agentID", agentID), zap.Error(err))
					errCh <- err
					return
				}

				s.logger.Info("发送任务配置", zap.String("agent", agentID), zap.String("任务", taskConfig.TaskID))
			case <-stream.Context().Done():
				s.logger.Info("stream context 已完成", zap.String("agentID", agentID))
				return
			}
		}
	}()

	// 等待任一 goroutine 出错或全部完成
	go func() {
		wg.Wait()
		close(errCh)
	}()

	// 等待错误或全部完成
	err = <-errCh
	return err
}

// StreamMetricData 实现 AgentService 的 StreamMetricData 方法
func (s *Server) StreamMetricData(stream pb.AgentService_StreamMetricDataServer) error {
	var agentID string
	var receivedCount int32

	for {
		pbMetric, err := stream.Recv()
		if err == io.EOF {
			s.logger.Info("Agent 指标数据流已关闭", zap.String("agentID", agentID))
			break
		}
		if err != nil {
			s.logger.Error("接收指标数据失败", zap.Error(err))
			return err
		}

		// 从首条消息中获取 agentID
		if agentID == "" {
			agentID = getAgentIDFromMetricData(pbMetric)
			s.logger.Info("Agent 连接到指标数据流", zap.String("agentID", agentID))
		}

		// 转换为模型
		metric := convertPbMetricToModel(pbMetric)

		// 批量保存（这里简化为单条保存）
		err = s.metricService.SaveMetrics([]*model.MetricData{metric})
		if err != nil {
			s.logger.Error("保存指标数据失败", zap.Error(err))
		} else {
			receivedCount++
		}
	}

	// 发送响应
	return stream.SendAndClose(&pb.StreamMetricDataResponse{
		Success:       true,
		Message:       fmt.Sprintf("成功接收 %d 条指标数据", receivedCount),
		ReceivedCount: receivedCount,
	})
}

// StreamLogData 实现 AgentService 的 StreamLogData 方法
func (s *Server) StreamLogData(stream pb.AgentService_StreamLogDataServer) error {
	var agentID string
	var receivedCount int32
	var logEntries []*model.LogEntry

	for {
		pbLogEntry, err := stream.Recv()
		if err == io.EOF {
			s.logger.Info("Agent 日志数据流已关闭", zap.String("agentID", agentID))
			break
		}
		if err != nil {
			s.logger.Error("接收日志数据失败", zap.Error(err))
			return err
		}

		// 从首条消息中获取 agentID
		if agentID == "" {
			agentID = pbLogEntry.DeviceId
			s.logger.Info("Agent 连接到日志数据流", zap.String("agentID", agentID))
		}

		// 转换为模型
		logEntry := convertPbLogEntryToModel(pbLogEntry)
		logEntries = append(logEntries, logEntry)
		receivedCount++

		// 批量保存，当达到一定数量时保存一次
		if len(logEntries) >= 100 {
			err = s.logService.SaveLogs(logEntries)
			if err != nil {
				s.logger.Error("保存日志数据失败", zap.Error(err))
			}
			logEntries = logEntries[:0] // 清空切片
		}
	}

	// 保存剩余的日志数据
	if len(logEntries) > 0 {
		err := s.logService.SaveLogs(logEntries)
		if err != nil {
			s.logger.Error("保存剩余日志数据失败", zap.Error(err))
		}
	}

	// 发送响应
	return stream.SendAndClose(&pb.StreamLogDataResponse{
		Success:       true,
		Message:       fmt.Sprintf("成功接收 %d 条日志数据", receivedCount),
		ReceivedCount: receivedCount,
	})
}

// processTaskStatus 处理来自 Agent 的任务状态更新
func (s *Server) processTaskStatus(agentID string, taskStatus model.TaskStatus) error {
	return s.agentService.UpdateTaskStatus(agentID, taskStatus)
}

// getAgentIDFromTaskStatus 从任务状态中获取 AgentID
func getAgentIDFromTaskStatus(status *pb.TaskStatus) string {
	// 使用完整的 TaskId 作为 AgentID
	return status.TaskId
}

// getAgentIDFromMetricData 从指标数据中获取 AgentID
func getAgentIDFromMetricData(metric *pb.MetricData) string {
	// 使用完整的 DeviceId 作为 AgentID
	return metric.DeviceId
}

// 转换函数
func convertPbTaskStatusToModel(pbStatus *pb.TaskStatus) model.TaskStatus {
	return model.TaskStatus{
		TaskID:               pbStatus.TaskId,
		Status:               pbStatus.Status,
		ErrorMessage:         pbStatus.ErrorMessage,
		LastCollectTimestamp: time.Unix(pbStatus.LastCollectTimestamp, 0), // Convert int64 to time.Time
	}
}

func convertPbMetricToModel(pbMetric *pb.MetricData) *model.MetricData {
	// 将 protobuf 格式的 labels 转换为 JSON 字符串
	labels := ""
	if len(pbMetric.Labels) > 0 {
		labelsJSON, err := json.Marshal(pbMetric.Labels)
		if err == nil {
			labels = string(labelsJSON)
		} else {
			labels = "{}" // Default to empty JSON object on error
		}
	}

	metric := &model.MetricData{
		DeviceID:  pbMetric.DeviceId,
		MetricKey: pbMetric.MetricKey,
		Timestamp: pbMetric.Timestamp,
		Labels:    labels,
	}

	// 根据 protobuf 中的 oneof 值类型设置相应的字段
	switch value := pbMetric.ValueType.(type) {
	case *pb.MetricData_NumericValue:
		metric.NumericValue = &value.NumericValue
	case *pb.MetricData_StringValue:
		metric.StringValue = &value.StringValue
	case *pb.MetricData_BooleanValue:
		metric.BooleanValue = &value.BooleanValue
	}

	// 如果有 JSON 数据，设置 JSONData 字段
	if pbMetric.JsonData != "" {
		metric.JSONData = pbMetric.JsonData
	}

	return metric
}

func convertPbLogEntryToModel(pbLogEntry *pb.LogEntry) *model.LogEntry {
	return &model.LogEntry{
		DeviceID:  pbLogEntry.DeviceId,
		LogLevel:  pbLogEntry.LogLevel,
		Message:   pbLogEntry.Message,
		Timestamp: pbLogEntry.Timestamp,
		Source:    pbLogEntry.Source,
	}
}

func convertModelTaskConfigToPb(config model.CollectorTaskConfig) *pb.CollectorTaskConfig {
	// 将 JSON 字符串转换为 map[string]string
	connectParams := make(map[string]string)
	if config.ConnectParams != "" {
		err := json.Unmarshal([]byte(config.ConnectParams), &connectParams) // Use json.Unmarshal
		if err != nil {
			// Handle error, e.g., log it or leave connectParams empty
		}
	}

	// 将 CollectItems JSON 字符串转换为 []string
	var collectItems []string
	if config.CollectItems != "" {
		err := json.Unmarshal([]byte(config.CollectItems), &collectItems) // Use json.Unmarshal
		if err != nil {
			// Handle error, e.g., log it or leave collectItems empty
		}
	}

	return &pb.CollectorTaskConfig{
		TaskId:           config.TaskID,
		DeviceId:         config.DeviceID,
		DeviceName:       config.DeviceName,
		DeviceType:       config.DeviceType,
		Host:             config.Host,
		Port:             int32(config.Port),
		Username:         config.Username,
		Password:         config.Password,
		ConnectParams:    connectParams,
		FrequencySeconds: int64(config.Frequency.Seconds()), // Convert time.Duration to int64 seconds
		CollectItems:     collectItems,
		IsEnabled:        config.IsEnabled,
	}
}
