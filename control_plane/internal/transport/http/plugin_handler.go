package http

import (
	"math/rand"
	"net/http"

	"aiops/control_plane/internal/service"
	pb "aiops/pkg/proto"
	plugininterface "aiops/plugins/interface"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PluginHandler 插件相关的 HTTP 处理器
type PluginHandler struct {
	pluginService *service.PluginService
	logger        *zap.Logger
}

// NewPluginHandler 创建新的插件处理器
func NewPluginHandler(pluginService *service.PluginService, logger *zap.Logger) *PluginHandler {
	return &PluginHandler{
		pluginService: pluginService,
		logger:        logger,
	}
}

// RegisterRoutes 注册插件相关路由
func (h *PluginHandler) RegisterRoutes(router *gin.RouterGroup) {
	plugins := router.Group("/plugins")
	{
		plugins.GET("", h.ListPlugins)
		plugins.GET("/:name/:version", h.GetPlugin)
		plugins.GET("/:name/:version/metrics", h.GetPluginMetrics)
		plugins.GET("/:name/:version/health", h.CheckPluginHealth)
		plugins.POST("/health-check", h.HealthCheckAllPlugins)

		// 采集器相关
		plugins.GET("/collectors", h.ListCollectors)
		plugins.GET("/collectors/supported-metrics/:deviceType", h.GetSupportedMetrics)

		// 分析功能
		plugins.POST("/analyze", h.AnalyzeMetrics)
		plugins.POST("/detect-anomalies", h.DetectAnomalies)
	}
}

// ListPlugins 列出所有插件
// @Summary 列出所有插件
// @Tags plugins
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/plugins [get]
func (h *PluginHandler) ListPlugins(c *gin.Context) {
	plugins := h.pluginService.ListPlugins()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"plugins": plugins,
			"count":   len(plugins),
		},
	})
}

// GetPlugin 获取指定插件信息
// @Summary 获取插件信息
// @Tags plugins
// @Param name path string true "插件名称"
// @Param version path string true "插件版本"
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Router /api/v1/plugins/{name}/{version} [get]
func (h *PluginHandler) GetPlugin(c *gin.Context) {
	name := c.Param("name")
	version := c.Param("version")

	if name == "" || version == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "插件名称和版本不能为空",
		})
		return
	}

	pluginInfo, err := h.pluginService.GetPluginInfo(name, version)
	if err != nil {
		h.logger.Error("Failed to get plugin info", zap.String("name", name), zap.String("version", version), zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "插件未找到",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    pluginInfo,
	})
}

// GetPluginMetrics 获取插件指标
// @Summary 获取插件指标
// @Tags plugins
// @Param name path string true "插件名称"
// @Param version path string true "插件版本"
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Router /api/v1/plugins/{name}/{version}/metrics [get]
func (h *PluginHandler) GetPluginMetrics(c *gin.Context) {
	name := c.Param("name")
	version := c.Param("version")

	if name == "" || version == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "插件名称和版本不能为空",
		})
		return
	}

	metrics, err := h.pluginService.GetPluginMetrics(c.Request.Context(), name, version)
	if err != nil {
		h.logger.Error("Failed to get plugin metrics", zap.String("name", name), zap.String("version", version), zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "获取插件指标失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// CheckPluginHealth 检查插件健康状态
// @Summary 检查插件健康状态
// @Tags plugins
// @Param name path string true "插件名称"
// @Param version path string true "插件版本"
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Router /api/v1/plugins/{name}/{version}/health [get]
func (h *PluginHandler) CheckPluginHealth(c *gin.Context) {
	name := c.Param("name")
	version := c.Param("version")

	if name == "" || version == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "插件名称和版本不能为空",
		})
		return
	}

	// 直接通过 registry 检查健康状态
	healthStatus := h.pluginService.HealthCheckPlugins(c.Request.Context())
	pluginKey := name + ":" + version

	if err, exists := healthStatus[pluginKey]; exists {
		if err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"success": false,
				"healthy": false,
				"error":   err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"healthy": true,
		})
		return
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success": false,
		"error":   "插件未找到",
	})
}

// HealthCheckAllPlugins 检查所有插件健康状态
// @Summary 检查所有插件健康状态
// @Tags plugins
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/plugins/health-check [post]
func (h *PluginHandler) HealthCheckAllPlugins(c *gin.Context) {
	healthStatus := h.pluginService.HealthCheckPlugins(c.Request.Context())

	healthyCount := 0
	unhealthyPlugins := make(map[string]string)

	for pluginKey, err := range healthStatus {
		if err != nil {
			unhealthyPlugins[pluginKey] = err.Error()
		} else {
			healthyCount++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total_plugins":     len(healthStatus),
			"healthy_plugins":   healthyCount,
			"unhealthy_plugins": len(unhealthyPlugins),
			"unhealthy_details": unhealthyPlugins,
		},
	})
}

// ListCollectors 列出所有采集器插件
// @Summary 列出所有采集器插件
// @Tags plugins
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/plugins/collectors [get]
func (h *PluginHandler) ListCollectors(c *gin.Context) {
	collectors := h.pluginService.GetAvailableCollectors()

	var collectorInfos []gin.H
	for _, collector := range collectors {
		info := collector.GetInfo()
		// 获取支持的设备类型
		var supportedDeviceTypes []string
		if enhancedCollector, ok := collector.(plugininterface.EnhancedCollector); ok {
			supportedDeviceTypes = enhancedCollector.GetSupportedDeviceTypes()
		}

		collectorInfos = append(collectorInfos, gin.H{
			"name":                   info.Name,
			"version":                info.Version,
			"description":            info.Description,
			"supported_device_types": supportedDeviceTypes,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"collectors": collectorInfos,
			"count":      len(collectorInfos),
		},
	})
}

// GetSupportedMetrics 获取设备类型支持的指标
// @Summary 获取设备类型支持的指标
// @Tags plugins
// @Param deviceType path string true "设备类型"
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/plugins/collectors/supported-metrics/{deviceType} [get]
func (h *PluginHandler) GetSupportedMetrics(c *gin.Context) {
	deviceType := c.Param("deviceType")

	if deviceType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "设备类型不能为空",
		})
		return
	}

	metrics, err := h.pluginService.GetSupportedMetrics(deviceType)
	if err != nil {
		h.logger.Error("Failed to get supported metrics", zap.String("deviceType", deviceType), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取支持的指标失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"device_type": deviceType,
			"metrics":     metrics,
			"count":       len(metrics),
		},
	})
}

// AnalyzeMetricsRequest 分析指标请求
type AnalyzeMetricsRequest struct {
	DeviceID  string `json:"device_id" binding:"required"`
	StartTime int64  `json:"start_time" binding:"required"`
	EndTime   int64  `json:"end_time" binding:"required"`
	Limit     int    `json:"limit,omitempty"`
}

// AnalyzeMetrics 分析指标数据
// @Summary 分析指标数据
// @Tags plugins
// @Accept json
// @Param request body AnalyzeMetricsRequest true "分析请求"
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/plugins/analyze [post]
func (h *PluginHandler) AnalyzeMetrics(c *gin.Context) {
	var req AnalyzeMetricsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数无效: " + err.Error(),
		})
		return
	}

	// 生成模拟指标数据用于演示
	testMetrics := h.generateTestMetrics(req.DeviceID, req.StartTime, req.EndTime, req.Limit)

	// 使用插件分析指标
	results, err := h.pluginService.AnalyzeMetrics(c.Request.Context(), testMetrics)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "分析失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "分析完成",
		"data": gin.H{
			"device_id":        req.DeviceID,
			"start_time":       req.StartTime,
			"end_time":         req.EndTime,
			"total_metrics":    len(testMetrics),
			"analysis_count":   len(results),
			"analysis_results": results,
		},
	})
}

// DetectAnomaliesRequest 异常检测请求
type DetectAnomaliesRequest struct {
	DeviceID  string  `json:"device_id" binding:"required"`
	StartTime int64   `json:"start_time" binding:"required"`
	EndTime   int64   `json:"end_time" binding:"required"`
	Threshold float64 `json:"threshold,omitempty"`
}

// DetectAnomalies 检测异常
// @Summary 检测异常
// @Tags plugins
// @Accept json
// @Param request body DetectAnomaliesRequest true "异常检测请求"
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/v1/plugins/detect-anomalies [post]
func (h *PluginHandler) DetectAnomalies(c *gin.Context) {
	var req DetectAnomaliesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数无效: " + err.Error(),
		})
		return
	}

	// 生成测试指标数据用于异常检测
	testMetrics := h.generateTestMetrics(req.DeviceID, req.StartTime, req.EndTime, 100)

	// 使用插件进行异常检测
	anomalies, err := h.pluginService.DetectAnomalies(c.Request.Context(), testMetrics)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "异常检测失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "异常检测完成",
		"data": gin.H{
			"device_id":     req.DeviceID,
			"start_time":    req.StartTime,
			"end_time":      req.EndTime,
			"threshold":     req.Threshold,
			"total_metrics": len(testMetrics),
			"anomalies":     anomalies,
			"anomaly_count": len(anomalies),
		},
	})
}

// generateTestMetrics 生成测试指标数据
func (h *PluginHandler) generateTestMetrics(deviceID string, startTime, endTime int64, limit int) []*pb.MetricData {
	if limit <= 0 {
		limit = 50 // 默认生成50个数据点
	}

	var metrics []*pb.MetricData
	timeStep := (endTime - startTime) / int64(limit)

	// 指标类型
	metricTypes := []string{"cpu_usage", "memory_usage", "disk_usage", "network_in", "network_out"}

	for i := range limit {
		timestamp := startTime + int64(i)*timeStep

		for _, metricType := range metricTypes {
			var metric *pb.MetricData

			switch metricType {
			case "cpu_usage":
				// CPU使用率: 0-100%，添加一些异常值
				cpu := 20.0 + rand.Float64()*60.0 // 基础范围20-80%
				if rand.Float64() < 0.2 {         // 20%概率产生异常高值 (增加概率)
					cpu = 95.0 + rand.Float64()*5.0 // 95-100%
				}
				metric = &pb.MetricData{
					DeviceId:  deviceID,
					MetricKey: metricType,
					ValueType: &pb.MetricData_NumericValue{NumericValue: cpu},
					Timestamp: timestamp,
					Labels: map[string]string{
						"source": "plugin_test",
						"type":   "system",
					},
				}

			case "memory_usage":
				// 内存使用率: 0-100%
				memory := 30.0 + rand.Float64()*50.0 // 基础范围30-80%
				if rand.Float64() < 0.15 {           // 15%概率产生异常高值
					memory = 90.0 + rand.Float64()*10.0 // 90-100%
				}
				metric = &pb.MetricData{
					DeviceId:  deviceID,
					MetricKey: metricType,
					ValueType: &pb.MetricData_NumericValue{NumericValue: memory},
					Timestamp: timestamp,
					Labels: map[string]string{
						"source": "plugin_test",
						"type":   "system",
					},
				}

			case "disk_usage":
				// 磁盘使用率: 0-100%
				disk := 40.0 + rand.Float64()*40.0 // 基础范围40-80%
				metric = &pb.MetricData{
					DeviceId:  deviceID,
					MetricKey: metricType,
					ValueType: &pb.MetricData_NumericValue{NumericValue: disk},
					Timestamp: timestamp,
					Labels: map[string]string{
						"source": "plugin_test",
						"type":   "system",
					},
				}

			case "network_in":
				// 网络入流量: MB/s
				netIn := rand.Float64() * 100.0 // 0-100 MB/s
				if rand.Float64() < 0.08 {      // 8%概率产生流量峰值
					netIn = 200.0 + rand.Float64()*300.0
				}
				metric = &pb.MetricData{
					DeviceId:  deviceID,
					MetricKey: metricType,
					ValueType: &pb.MetricData_NumericValue{NumericValue: netIn},
					Timestamp: timestamp,
					Labels: map[string]string{
						"source": "plugin_test",
						"type":   "network",
					},
				}

			case "network_out":
				// 网络出流量: MB/s
				netOut := rand.Float64() * 50.0 // 0-50 MB/s
				if rand.Float64() < 0.06 {      // 6%概率产生流量峰值
					netOut = 100.0 + rand.Float64()*200.0
				}
				metric = &pb.MetricData{
					DeviceId:  deviceID,
					MetricKey: metricType,
					ValueType: &pb.MetricData_NumericValue{NumericValue: netOut},
					Timestamp: timestamp,
					Labels: map[string]string{
						"source": "plugin_test",
						"type":   "network",
					},
				}
			}

			metrics = append(metrics, metric)
		}
	}

	return metrics
}
