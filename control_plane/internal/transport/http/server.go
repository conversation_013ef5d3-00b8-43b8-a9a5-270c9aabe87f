package http

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"aiops/control_plane/config"
	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/service"
	"aiops/pkg/log"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Server 是 HTTP API 服务器的实现
type Server struct {
	config                 *config.Config
	router                 *gin.Engine
	httpServer             *http.Server
	agentService           *service.AgentService
	deviceService          *service.DeviceService
	taskService            *service.CollectorTaskService
	metricService          *service.MetricService
	alertService           *service.AlertService
	userService            *service.UserService
	logService             service.LogService
	supportedMetricService service.SupportedMetricService
	pluginService          *service.PluginService
	logger                 *log.Logger
}

// NewServer 创建一个新的 HTTP API 服务器
func NewServer(
	config *config.Config,
	agentService *service.AgentService,
	deviceService *service.DeviceService,
	taskService *service.CollectorTaskService,
	metricService *service.MetricService,
	alertService *service.AlertService,
	userService *service.UserService,
	logService service.LogService,
	supportedMetricService service.SupportedMetricService,
	pluginService *service.PluginService,
	logger *log.Logger,
) *Server {
	router := gin.New()
	gin.SetMode(gin.DebugMode)
	router.Use(gin.Recovery())

	server := &Server{
		config:                 config,
		router:                 router,
		agentService:           agentService,
		deviceService:          deviceService,
		taskService:            taskService,
		metricService:          metricService,
		alertService:           alertService,
		userService:            userService,
		logService:             logService,
		supportedMetricService: supportedMetricService,
		pluginService:          pluginService,
		logger:                 logger,
	}

	server.setupRoutes()

	return server
}

// Start 启动 HTTP API 服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("0.0.0.0:%d", s.config.HTTPPort)
	s.httpServer = &http.Server{
		Addr:    addr,
		Handler: s.router,
	}

	s.logger.Info("启动 HTTP API 服务器", zap.String("监听地址", addr))

	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Error("HTTP 服务器异常退出", zap.Error(err))
		}
	}()

	return nil
}

// Stop 停止 HTTP API 服务器
func (s *Server) Stop() error {
	if s.httpServer != nil {
		s.logger.Info("正在停止 HTTP API 服务器")

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := s.httpServer.Shutdown(ctx); err != nil {
			return fmt.Errorf("HTTP 服务器关闭失败: %v", err)
		}

		s.logger.Info("HTTP API 服务器已停止")
	}

	return nil
}

// setupRoutes 设置 API 路由
func (s *Server) setupRoutes() {
	// 健康检查端点（无需认证）
	s.router.GET("/api/health", s.handleHealth)

	// 中间件
	authMiddleware := s.authMiddleware()

	// 认证相关的路由
	auth := s.router.Group("/api/auth")
	{
		auth.POST("/login", s.handleLogin)
		auth.POST("/logout", authMiddleware, s.handleLogout)
	}

	// API 路由组
	api := s.router.Group("/api/v1", authMiddleware)
	{
		// Agent 相关路由
		agents := api.Group("/agents")
		{
			agents.GET("", s.handleGetAllAgents)
			agents.GET("/:id", s.handleGetAgent)
			agents.DELETE("/:id", s.handleDeleteAgent)
		}

		// 设备相关路由
		devices := api.Group("/devices")
		{
			devices.GET("", s.handleGetAllDevices)
			devices.POST("", s.handleCreateDevice)
			devices.GET("/:id", s.handleGetDevice)
			devices.PUT("/:id", s.handleUpdateDevice)
			devices.DELETE("/:id", s.handleDeleteDevice)
		}

		// 采集任务相关路由
		tasks := api.Group("/tasks")
		{
			tasks.GET("", s.handleGetAllTasks)
			tasks.POST("", s.handleCreateTask)
			tasks.GET("/:id", s.handleGetTask)
			tasks.PUT("/:id", s.handleUpdateTask)
			tasks.DELETE("/:id", s.handleDeleteTask)
			tasks.POST("/:id/enable", s.handleEnableTask)
			tasks.POST("/:id/disable", s.handleDisableTask)
		}

		// 指标数据相关路由
		metrics := api.Group("/metrics")
		{
			metrics.GET("/device/:deviceId", s.handleGetDeviceMetrics)
			metrics.GET("/device/:deviceId/key/:key", s.handleGetMetricByKey)
			metrics.GET("/device/:deviceId/latest", s.handleGetLatestDeviceMetrics)
		}

		// 支持的指标管理路由
		supportedMetrics := api.Group("/supported-metrics")
		{
			supportedMetrics.GET("", s.handleGetAllSupportedMetrics)
			supportedMetrics.POST("", s.handleCreateSupportedMetric)
			supportedMetrics.GET("/:id", s.handleGetSupportedMetric)
			supportedMetrics.PUT("/:id", s.handleUpdateSupportedMetric)
			supportedMetrics.DELETE("/:id", s.handleDeleteSupportedMetric)
			supportedMetrics.GET("/collector/:type", s.handleGetSupportedMetricsByCollectorType)
		}

		// 日志数据相关路由
		logs := api.Group("/logs")
		{
			logs.GET("/device/:deviceId", s.handleGetDeviceLogs)
			logs.GET("", s.handleGetLogs)
			logs.GET("/sources", s.handleGetLogSources)
			logs.DELETE("/cleanup", s.adminOnly, s.handleCleanupOldLogs)
		}

		// 告警相关路由
		alerts := api.Group("/alerts")
		{
			alerts.GET("/rules", s.handleGetAllAlertRules)
			alerts.POST("/rules", s.handleCreateAlertRule)
			alerts.GET("/rules/:id", s.handleGetAlertRule)
			alerts.PUT("/rules/:id", s.handleUpdateAlertRule)
			alerts.DELETE("/rules/:id", s.handleDeleteAlertRule)

			alerts.GET("/events", s.handleGetAlertEvents)
			alerts.GET("/events/active", s.handleGetActiveAlerts)
			alerts.POST("/events/:id/resolve", s.handleResolveAlert)
		}

		// 用户相关路由
		users := api.Group("/users")
		{
			users.GET("", s.adminOnly, s.handleGetAllUsers)
			users.POST("", s.adminOnly, s.handleCreateUser)
			users.GET("/:id", s.userOwnOrAdmin, s.handleGetUser)
			users.PUT("/:id", s.userOwnOrAdmin, s.handleUpdateUser)
			users.DELETE("/:id", s.adminOnly, s.handleDeleteUser)
			users.POST("/:id/password", s.userOwnOrAdmin, s.handleChangePassword)
		}

		// 插件相关路由
		if s.pluginService != nil {
			pluginHandler := NewPluginHandler(s.pluginService, s.logger.Logger)
			pluginHandler.RegisterRoutes(api)
		}
	}
}

// authMiddleware 是认证中间件
func (s *Server) authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取认证令牌
		token := c.GetHeader("Authorization")
		if token == "" {
			token = c.Query("token")
		}

		// 验证令牌
		// 实际项目中应使用 JWT 或其他认证机制
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "未提供认证令牌",
			})
			c.Abort()
			return
		}

		// TODO: 实现真实的令牌验证
		// 这里简化为固定令牌
		if token != "dev-token" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "认证令牌无效",
			})
			c.Abort()
			return
		}

		// 设置用户信息
		c.Set("userId", uint(1))
		c.Set("role", "admin")

		c.Next()
	}
}

// adminOnly 只允许管理员访问
func (s *Server) adminOnly(c *gin.Context) {
	role, exists := c.Get("role")
	if !exists || role != "admin" {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "需要管理员权限",
		})
		c.Abort()
		return
	}

	c.Next()
}

// userOwnOrAdmin 只允许用户自己或管理员访问
func (s *Server) userOwnOrAdmin(c *gin.Context) {
	role, _ := c.Get("role")
	userId, _ := c.Get("userId")

	// 如果是管理员，允许访问
	if role == "admin" {
		c.Next()
		return
	}

	// 检查是否是用户自己的资源
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户 ID",
		})
		c.Abort()
		return
	}

	if uint(id) != userId.(uint) {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权访问该资源",
		})
		c.Abort()
		return
	}

	c.Next()
}

// API 处理函数

// 健康检查
func (s *Server) handleHealth(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":    "ok",
		"message":   "DevInsight Control Plane is running",
		"timestamp": time.Now().Unix(),
	})
}

// 登录和注销
func (s *Server) handleLogin(c *gin.Context) {
	var req struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的请求参数",
		})
		return
	}

	user, authenticated, err := s.userService.Authenticate(req.Username, req.Password)
	if err != nil {
		s.logger.Error("用户认证错误", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "认证过程中发生错误",
		})
		return
	}

	if !authenticated {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户名或密码不正确",
		})
		return
	}

	// TODO: 生成真实的 JWT 令牌
	token := "dev-token"

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"token":    token,
			"userId":   user.ID,
			"username": user.Username,
			"role":     user.Role,
		},
	})
}

func (s *Server) handleLogout(c *gin.Context) {
	// 实际项目中，可能需要使 token 失效
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "注销成功",
	})
}

// Agent 相关处理函数
func (s *Server) handleGetAllAgents(c *gin.Context) {
	agents, err := s.agentService.GetAllAgents()
	if err != nil {
		s.logger.Error("获取所有 Agent 失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取 Agent 列表失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    agents,
	})
}

func (s *Server) handleGetAgent(c *gin.Context) {
	agentID := c.Param("id")

	agent, err := s.agentService.GetAgent(agentID)
	if err != nil {
		s.logger.Error("获取 Agent 失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取 Agent 信息失败: %v", err),
		})
		return
	}

	if agent == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Agent 不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    agent,
	})
}

func (s *Server) handleDeleteAgent(c *gin.Context) {
	agentID := c.Param("id")

	err := s.agentService.DeleteAgent(agentID)
	if err != nil {
		s.logger.Error("删除 Agent 失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("删除 Agent 失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Agent 删除成功",
	})
}

// 设备相关处理函数
func (s *Server) handleGetAllDevices(c *gin.Context) {
	devices, err := s.deviceService.GetAllDevices()
	if err != nil {
		s.logger.Error("获取所有设备失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取设备列表失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    devices,
	})
}

func (s *Server) handleCreateDevice(c *gin.Context) {
	// TODO: 简化的方法，实际项目中需要更复杂的处理
	// 此方法仅供示例，不一一列出所有 handler
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "创建设备成功",
	})
}

func (s *Server) handleGetDevice(c *gin.Context) {
	// 类似处理
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取设备成功",
	})
}

func (s *Server) handleUpdateDevice(c *gin.Context) {
	// 类似处理
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新设备成功",
	})
}

func (s *Server) handleDeleteDevice(c *gin.Context) {
	// 类似处理
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除设备成功",
	})
}

// 采集任务相关处理函数
func (s *Server) handleGetAllTasks(c *gin.Context) {
	// 实现略
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取所有任务成功",
	})
}

func (s *Server) handleCreateTask(c *gin.Context) {
	// 实现略
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "创建任务成功",
	})
}

func (s *Server) handleGetTask(c *gin.Context) {
	// 实现略
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取任务成功",
	})
}

func (s *Server) handleUpdateTask(c *gin.Context) {
	// 实现略
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新任务成功",
	})
}

func (s *Server) handleDeleteTask(c *gin.Context) {
	// 实现略
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除任务成功",
	})
}

func (s *Server) handleEnableTask(c *gin.Context) {
	// 实现略
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "启用任务成功",
	})
}

func (s *Server) handleDisableTask(c *gin.Context) {
	// 实现略
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "禁用任务成功",
	})
}

// 指标数据相关处理函数
func (s *Server) handleGetDeviceMetrics(c *gin.Context) {
	// 实现略
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取设备指标数据成功",
	})
}

func (s *Server) handleGetMetricByKey(c *gin.Context) {
	// 实现略
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取指标数据成功",
	})
}

func (s *Server) handleGetLatestDeviceMetrics(c *gin.Context) {
	// 实现略
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取最新指标数据成功",
	})
}

// SupportedMetric 处理器

// handleGetAllSupportedMetrics 获取所有支持的指标
func (s *Server) handleGetAllSupportedMetrics(c *gin.Context) {
	supportedMetrics, err := s.supportedMetricService.GetAllSupportedMetrics()
	if err != nil {
		s.logger.Error("获取支持的指标失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取支持的指标失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": supportedMetrics})
}

// handleCreateSupportedMetric 创建支持的指标
func (s *Server) handleCreateSupportedMetric(c *gin.Context) {
	var req struct {
		MetricKey     string            `json:"metric_key" binding:"required"`
		MetricName    string            `json:"metric_name" binding:"required"`
		Description   string            `json:"description"`
		DataType      string            `json:"data_type" binding:"required"`
		Unit          string            `json:"unit"`
		Metadata      map[string]string `json:"metadata"`
		IsActive      bool              `json:"is_active"`
		CollectorType string            `json:"collector_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	supportedMetric := &model.SupportedMetric{
		MetricKey:     req.MetricKey,
		MetricName:    req.MetricName,
		Description:   req.Description,
		DataType:      req.DataType,
		Unit:          req.Unit,
		IsActive:      req.IsActive,
		CollectorType: req.CollectorType,
	}

	// 处理 metadata
	if len(req.Metadata) > 0 {
		metadataJSON, err := json.Marshal(req.Metadata)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "无效的元数据格式",
			})
			return
		}
		supportedMetric.Metadata = string(metadataJSON)
	}

	err := s.supportedMetricService.CreateSupportedMetric(supportedMetric)
	if err != nil {
		s.logger.Error("创建支持的指标失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建支持的指标失败"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": supportedMetric})
}

// handleGetSupportedMetric 获取指定支持的指标
func (s *Server) handleGetSupportedMetric(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的ID"})
		return
	}

	supportedMetric, err := s.supportedMetricService.GetSupportedMetricByID(uint(id))
	if err != nil {
		s.logger.Error("获取支持的指标失败", zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "支持的指标不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": supportedMetric})
}

// handleUpdateSupportedMetric 更新支持的指标
func (s *Server) handleUpdateSupportedMetric(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的ID"})
		return
	}

	var req struct {
		MetricName    string            `json:"metric_name"`
		Description   string            `json:"description"`
		DataType      string            `json:"data_type"`
		Unit          string            `json:"unit"`
		Metadata      map[string]string `json:"metadata"`
		IsActive      *bool             `json:"is_active"`
		CollectorType string            `json:"collector_type"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 首先获取现有的支持指标
	supportedMetric, err := s.supportedMetricService.GetSupportedMetricByID(uint(id))
	if err != nil {
		s.logger.Error("获取支持的指标失败", zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "支持的指标不存在"})
		return
	}

	// 更新字段
	if req.MetricName != "" {
		supportedMetric.MetricName = req.MetricName
	}
	if req.Description != "" {
		supportedMetric.Description = req.Description
	}
	if req.DataType != "" {
		supportedMetric.DataType = req.DataType
	}
	if req.Unit != "" {
		supportedMetric.Unit = req.Unit
	}
	if req.CollectorType != "" {
		supportedMetric.CollectorType = req.CollectorType
	}
	if req.IsActive != nil {
		supportedMetric.IsActive = *req.IsActive
	}
	if len(req.Metadata) > 0 {
		metadataJSON, jsonErr := json.Marshal(req.Metadata)
		if jsonErr != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "无效的元数据格式",
			})
			return
		}
		supportedMetric.Metadata = string(metadataJSON)
	}

	err = s.supportedMetricService.UpdateSupportedMetric(supportedMetric)
	if err != nil {
		s.logger.Error("更新支持的指标失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新支持的指标失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": supportedMetric})
}

// handleDeleteSupportedMetric 删除支持的指标
func (s *Server) handleDeleteSupportedMetric(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的ID"})
		return
	}

	err = s.supportedMetricService.DeleteSupportedMetric(uint(id))
	if err != nil {
		s.logger.Error("删除支持的指标失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "删除支持的指标失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "支持的指标已删除"})
}

// handleGetSupportedMetricsByCollectorType 根据采集器类型获取支持的指标
func (s *Server) handleGetSupportedMetricsByCollectorType(c *gin.Context) {
	collectorType := c.Param("type")

	supportedMetrics, err := s.supportedMetricService.GetSupportedMetricsByCollectorType(collectorType)
	if err != nil {
		s.logger.Error("获取采集器支持的指标失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取采集器支持的指标失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": supportedMetrics})
}

// Log 处理器

// handleGetDeviceLogs 获取设备日志
func (s *Server) handleGetDeviceLogs(c *gin.Context) {
	deviceID := c.Param("deviceId")
	logLevel := c.Query("log_level")
	limitStr := c.DefaultQuery("limit", "100")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 100
	}

	// 解析时间范围
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	var startTime, endTime time.Time
	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "无效的开始时间格式"})
			return
		}
	} else {
		startTime = time.Now().Add(-24 * time.Hour) // 默认最近24小时
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "无效的结束时间格式"})
			return
		}
	} else {
		endTime = time.Now()
	}

	logs, err := s.logService.GetLogsByDeviceID(deviceID, startTime, endTime, logLevel, limit)
	if err != nil {
		s.logger.Error("获取设备日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取设备日志失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": logs})
}

// handleGetLogs 获取所有日志
func (s *Server) handleGetLogs(c *gin.Context) {
	logLevel := c.Query("log_level")
	limitStr := c.DefaultQuery("limit", "100")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 100
	}

	// 解析时间范围
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	var startTime, endTime time.Time
	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "无效的开始时间格式"})
			return
		}
	} else {
		startTime = time.Now().Add(-24 * time.Hour) // 默认最近24小时
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "无效的结束时间格式"})
			return
		}
	} else {
		endTime = time.Now()
	}

	logs, err := s.logService.GetLogsByTimeRange(startTime, endTime, logLevel, limit)
	if err != nil {
		s.logger.Error("获取日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取日志失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": logs})
}

// handleGetLogSources 获取日志来源列表
func (s *Server) handleGetLogSources(c *gin.Context) {
	// 这里可以实现获取不同来源的逻辑，暂时返回预定义的列表
	sources := []string{"agent", "collector", "control_plane", "system"}
	c.JSON(http.StatusOK, gin.H{"data": sources})
}

// handleCleanupOldLogs 清理旧日志
func (s *Server) handleCleanupOldLogs(c *gin.Context) {
	retentionDaysStr := c.DefaultQuery("retention_days", "30")
	retentionDays, err := strconv.Atoi(retentionDaysStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的保留天数"})
		return
	}

	err = s.logService.CleanupOldLogs(retentionDays)
	if err != nil {
		s.logger.Error("清理旧日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "清理旧日志失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": fmt.Sprintf("已清理超过 %d 天的旧日志", retentionDays)})
}

// Alert 处理器

// handleGetAllAlertRules 获取所有告警规则
func (s *Server) handleGetAllAlertRules(c *gin.Context) {
	rules, err := s.alertService.GetAllAlertRules()
	if err != nil {
		s.logger.Error("获取告警规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取告警规则失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rules,
	})
}

// handleCreateAlertRule 创建告警规则
func (s *Server) handleCreateAlertRule(c *gin.Context) {
	var req struct {
		Name        string  `json:"name" binding:"required"`
		Description string  `json:"description"`
		DeviceID    string  `json:"device_id" binding:"required"`
		MetricKey   string  `json:"metric_key" binding:"required"`
		Operator    string  `json:"operator" binding:"required"`
		Threshold   float64 `json:"threshold" binding:"required"`
		Severity    string  `json:"severity" binding:"required"`
		IsEnabled   bool    `json:"is_enabled"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的请求参数",
		})
		return
	}

	// 转换 DeviceID 字符串到 uint
	deviceID, err := strconv.ParseUint(req.DeviceID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的设备ID",
		})
		return
	}

	rule := &model.AlertRule{
		RuleName:       req.Name,
		DeviceID:       uint(deviceID),
		MetricKey:      req.MetricKey,
		ThresholdOp:    req.Operator,
		ThresholdValue: req.Threshold,
		IsEnabled:      req.IsEnabled,
	}

	err = s.alertService.CreateAlertRule(rule)
	if err != nil {
		s.logger.Error("创建告警规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("创建告警规则失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    rule,
	})
}

// handleGetAlertRule 获取告警规则
func (s *Server) handleGetAlertRule(c *gin.Context) {
	ruleIDStr := c.Param("id")
	ruleID, err := strconv.ParseUint(ruleIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的规则ID",
		})
		return
	}

	rule, err := s.alertService.GetAlertRule(uint(ruleID))
	if err != nil {
		s.logger.Error("获取告警规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取告警规则失败: %v", err),
		})
		return
	}

	if rule == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "告警规则不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rule,
	})
}

// handleUpdateAlertRule 更新告警规则
func (s *Server) handleUpdateAlertRule(c *gin.Context) {
	ruleIDStr := c.Param("id")
	ruleID, err := strconv.ParseUint(ruleIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的规则ID",
		})
		return
	}

	var req struct {
		Name        string   `json:"name"`
		Description string   `json:"description"`
		DeviceID    string   `json:"device_id"`
		MetricKey   string   `json:"metric_key"`
		Operator    string   `json:"operator"`
		Threshold   *float64 `json:"threshold"`
		Severity    string   `json:"severity"`
		IsEnabled   *bool    `json:"is_enabled"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的请求参数",
		})
		return
	}

	// 获取现有规则
	existingRule, err := s.alertService.GetAlertRule(uint(ruleID))
	if err != nil {
		s.logger.Error("获取告警规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取告警规则失败: %v", err),
		})
		return
	}

	if existingRule == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "告警规则不存在",
		})
		return
	}

	// 更新规则字段
	if req.Name != "" {
		existingRule.RuleName = req.Name
	}
	if req.MetricKey != "" {
		existingRule.MetricKey = req.MetricKey
	}
	if req.Operator != "" {
		existingRule.ThresholdOp = req.Operator
	}
	if req.Threshold != nil {
		existingRule.ThresholdValue = *req.Threshold
	}
	if req.IsEnabled != nil {
		existingRule.IsEnabled = *req.IsEnabled
	}
	if req.DeviceID != "" {
		deviceID, err := strconv.ParseUint(req.DeviceID, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "无效的设备ID",
			})
			return
		}
		existingRule.DeviceID = uint(deviceID)
	}

	err = s.alertService.UpdateAlertRule(existingRule)
	if err != nil {
		s.logger.Error("更新告警规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("更新告警规则失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    existingRule,
	})
}

// handleDeleteAlertRule 删除告警规则
func (s *Server) handleDeleteAlertRule(c *gin.Context) {
	ruleIDStr := c.Param("id")
	ruleID, err := strconv.ParseUint(ruleIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的规则ID",
		})
		return
	}

	err = s.alertService.DeleteAlertRule(uint(ruleID))
	if err != nil {
		s.logger.Error("删除告警规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("删除告警规则失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "告警规则已删除",
	})
}

// handleGetAlertEvents 获取告警事件
func (s *Server) handleGetAlertEvents(c *gin.Context) {
	// 从查询参数获取时间范围和限制
	limitStr := c.DefaultQuery("limit", "100")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 100
	}

	// 获取时间范围，默认最近24小时
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)

	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			startTime = t
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			endTime = t
		}
	}

	events, err := s.alertService.GetAlertEvents(startTime, endTime, limit)
	if err != nil {
		s.logger.Error("获取告警事件失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取告警事件失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    events,
	})
}

// handleGetActiveAlerts 获取活跃告警
func (s *Server) handleGetActiveAlerts(c *gin.Context) {
	alerts, err := s.alertService.GetActiveAlerts()
	if err != nil {
		s.logger.Error("获取活跃告警失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取活跃告警失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    alerts,
	})
}

// handleResolveAlert 解决告警
func (s *Server) handleResolveAlert(c *gin.Context) {
	alertIDStr := c.Param("id")
	alertID, err := strconv.ParseUint(alertIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的告警ID",
		})
		return
	}

	err = s.alertService.ResolveAlert(uint(alertID))
	if err != nil {
		s.logger.Error("解决告警失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("解决告警失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "告警已解决",
	})
}

// User 处理器

// handleGetAllUsers 获取所有用户
func (s *Server) handleGetAllUsers(c *gin.Context) {
	users, err := s.userService.GetAllUsers()
	if err != nil {
		s.logger.Error("获取用户列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取用户列表失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    users,
	})
}

// handleCreateUser 创建用户
func (s *Server) handleCreateUser(c *gin.Context) {
	var req struct {
		Username string `json:"username" binding:"required"`
		Password string `json:"password" binding:"required"`
		Role     string `json:"role" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的请求参数",
		})
		return
	}

	user := &model.User{
		Username:     req.Username,
		PasswordHash: req.Password, // 服务层会处理密码哈希
		Role:         req.Role,
	}

	err := s.userService.CreateUser(user)
	if err != nil {
		s.logger.Error("创建用户失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("创建用户失败: %v", err),
		})
		return
	}

	// 清除密码信息再返回
	user.PasswordHash = ""
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    user,
	})
}

// handleGetUser 获取用户
func (s *Server) handleGetUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	user, err := s.userService.GetUser(uint(userID))
	if err != nil {
		s.logger.Error("获取用户失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取用户失败: %v", err),
		})
		return
	}

	if user == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "用户不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    user,
	})
}

// handleUpdateUser 更新用户
func (s *Server) handleUpdateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	var req struct {
		Username string `json:"username"`
		Role     string `json:"role"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的请求参数",
		})
		return
	}

	// 获取现有用户
	existingUser, err := s.userService.GetUser(uint(userID))
	if err != nil {
		s.logger.Error("获取用户失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("获取用户失败: %v", err),
		})
		return
	}

	if existingUser == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "用户不存在",
		})
		return
	}

	// 更新用户字段
	if req.Username != "" {
		existingUser.Username = req.Username
	}
	if req.Role != "" {
		existingUser.Role = req.Role
	}

	err = s.userService.UpdateUser(existingUser)
	if err != nil {
		s.logger.Error("更新用户失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("更新用户失败: %v", err),
		})
		return
	}

	// 清除密码信息再返回
	existingUser.PasswordHash = ""
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    existingUser,
	})
}

// handleDeleteUser 删除用户
func (s *Server) handleDeleteUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	err = s.userService.DeleteUser(uint(userID))
	if err != nil {
		s.logger.Error("删除用户失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("删除用户失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户已删除",
	})
}

// handleChangePassword 修改密码
func (s *Server) handleChangePassword(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	var req struct {
		OldPassword string `json:"old_password" binding:"required"`
		NewPassword string `json:"new_password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的请求参数",
		})
		return
	}

	err = s.userService.ChangePassword(uint(userID), req.OldPassword, req.NewPassword)
	if err != nil {
		s.logger.Error("修改密码失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("修改密码失败: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "密码修改成功",
	})
}
