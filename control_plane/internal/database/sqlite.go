package database

import (
	"fmt"
	"time"

	"aiops/control_plane/internal/model"
	"aiops/pkg/log"
	"aiops/pkg/zapgorm2"

	"go.uber.org/zap"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// InitDatabase 初始化 SQLite 数据库并执行 GORM 自动迁移
func InitDatabase(dbPath string, logger *log.Logger) (*gorm.DB, error) {
	logger.Info("初始化数据库", zap.String("dbPath", dbPath))

	// 配置 GORM 日志
	gormLogger := zapgorm2.New(logger.Logger)
	// 打开 SQLite 数据库连接
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: gormLogger,
		// record not found
		// 忽略 ErrRecordNotFound 错误
		SkipDefaultTransaction: true,
	})
	if err != nil {
		return nil, fmt.Errorf("无法连接到数据库: %w", err)
	}
	logger.Info("数据库连接成功")

	// 获取底层 sql.DB 连接
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("无法获取底层数据库连接: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 启用 WAL 模式以提高并发写入性能
	if _, err := sqlDB.Exec("PRAGMA journal_mode=WAL;"); err != nil {
		logger.Error("启用 WAL 模式失败: %v", zap.Error(err))
	} else {
		logger.Info("SQLite WAL 模式启用成功")
	}

	// 自动迁移所有模型
	logger.Info("开始执行数据库迁移")
	err = db.AutoMigrate(
		&model.Agent{},
		&model.Device{},
		&model.CollectorTask{},
		&model.MetricData{},
		&model.AlertRule{},
		&model.AlertEvent{},
		&model.User{},
		&model.SupportedMetric{},
		&model.LogEntry{},
	)
	if err != nil {
		return nil, fmt.Errorf("数据库迁移失败: %w", err)
	}
	logger.Info("数据库迁移完成")

	return db, nil
}
