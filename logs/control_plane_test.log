load conf file: config/control_plane.yaml
2025-05-24 23:35:55.459956000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025/05/24 23:35:55 配置加载完成: HTTP端口=8080, gRPC端口=50051, 数据库路径=data/control_plane.db?_busy_timeout=5000, SMTP服务器=:587, 告警阈值=90.00
2025-05-24 23:35:55.509207000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-24 23:35:55.689924000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-24 23:35:55.690005000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-24 23:35:55.690020000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-24 23:35:55.711409000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-24 23:35:55.711457000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-24 23:35:55.712790000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-24 23:35:55.713954000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:75	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /api/health               --> aiops/control_plane/internal/transport/http.(*Server).handleHealth-fm (2 handlers)
[GIN-debug] POST   /api/auth/login           --> aiops/control_plane/internal/transport/http.(*Server).handleLogin-fm (2 handlers)
[GIN-debug] POST   /api/auth/logout          --> aiops/control_plane/internal/transport/http.(*Server).handleLogout-fm (3 handlers)
[GIN-debug] GET    /api/agents               --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllAgents-fm (3 handlers)
[GIN-debug] GET    /api/agents/:id           --> aiops/control_plane/internal/transport/http.(*Server).handleGetAgent-fm (3 handlers)
[GIN-debug] DELETE /api/agents/:id           --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteAgent-fm (3 handlers)
[GIN-debug] GET    /api/devices              --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllDevices-fm (3 handlers)
[GIN-debug] POST   /api/devices              --> aiops/control_plane/internal/transport/http.(*Server).handleCreateDevice-fm (3 handlers)
[GIN-debug] GET    /api/devices/:id          --> aiops/control_plane/internal/transport/http.(*Server).handleGetDevice-fm (3 handlers)
[GIN-debug] PUT    /api/devices/:id          --> aiops/control_plane/internal/transport/http.(*Server).handleUpdateDevice-fm (3 handlers)
[GIN-debug] DELETE /api/devices/:id          --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteDevice-fm (3 handlers)
[GIN-debug] GET    /api/tasks                --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllTasks-fm (3 handlers)
[GIN-debug] POST   /api/tasks                --> aiops/control_plane/internal/transport/http.(*Server).handleCreateTask-fm (3 handlers)
[GIN-debug] GET    /api/tasks/:id            --> aiops/control_plane/internal/transport/http.(*Server).handleGetTask-fm (3 handlers)
[GIN-debug] PUT    /api/tasks/:id            --> aiops/control_plane/internal/transport/http.(*Server).handleUpdateTask-fm (3 handlers)
[GIN-debug] DELETE /api/tasks/:id            --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteTask-fm (3 handlers)
[GIN-debug] POST   /api/tasks/:id/enable     --> aiops/control_plane/internal/transport/http.(*Server).handleEnableTask-fm (3 handlers)
[GIN-debug] POST   /api/tasks/:id/disable    --> aiops/control_plane/internal/transport/http.(*Server).handleDisableTask-fm (3 handlers)
[GIN-debug] GET    /api/metrics/device/:deviceId --> aiops/control_plane/internal/transport/http.(*Server).handleGetDeviceMetrics-fm (3 handlers)
[GIN-debug] GET    /api/metrics/device/:deviceId/key/:key --> aiops/control_plane/internal/transport/http.(*Server).handleGetMetricByKey-fm (3 handlers)
[GIN-debug] GET    /api/metrics/device/:deviceId/latest --> aiops/control_plane/internal/transport/http.(*Server).handleGetLatestDeviceMetrics-fm (3 handlers)
[GIN-debug] GET    /api/supported-metrics    --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllSupportedMetrics-fm (3 handlers)
[GIN-debug] POST   /api/supported-metrics    --> aiops/control_plane/internal/transport/http.(*Server).handleCreateSupportedMetric-fm (3 handlers)
[GIN-debug] GET    /api/supported-metrics/:id --> aiops/control_plane/internal/transport/http.(*Server).handleGetSupportedMetric-fm (3 handlers)
[GIN-debug] PUT    /api/supported-metrics/:id --> aiops/control_plane/internal/transport/http.(*Server).handleUpdateSupportedMetric-fm (3 handlers)
[GIN-debug] DELETE /api/supported-metrics/:id --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteSupportedMetric-fm (3 handlers)
[GIN-debug] GET    /api/supported-metrics/collector/:type --> aiops/control_plane/internal/transport/http.(*Server).handleGetSupportedMetricsByCollectorType-fm (3 handlers)
[GIN-debug] GET    /api/logs/device/:deviceId --> aiops/control_plane/internal/transport/http.(*Server).handleGetDeviceLogs-fm (3 handlers)
[GIN-debug] GET    /api/logs                 --> aiops/control_plane/internal/transport/http.(*Server).handleGetLogs-fm (3 handlers)
[GIN-debug] GET    /api/logs/sources         --> aiops/control_plane/internal/transport/http.(*Server).handleGetLogSources-fm (3 handlers)
[GIN-debug] DELETE /api/logs/cleanup         --> aiops/control_plane/internal/transport/http.(*Server).handleCleanupOldLogs-fm (4 handlers)
[GIN-debug] GET    /api/alerts/rules         --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllAlertRules-fm (3 handlers)
[GIN-debug] POST   /api/alerts/rules         --> aiops/control_plane/internal/transport/http.(*Server).handleCreateAlertRule-fm (3 handlers)
[GIN-debug] GET    /api/alerts/rules/:id     --> aiops/control_plane/internal/transport/http.(*Server).handleGetAlertRule-fm (3 handlers)
[GIN-debug] PUT    /api/alerts/rules/:id     --> aiops/control_plane/internal/transport/http.(*Server).handleUpdateAlertRule-fm (3 handlers)
[GIN-debug] DELETE /api/alerts/rules/:id     --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteAlertRule-fm (3 handlers)
[GIN-debug] GET    /api/alerts/events        --> aiops/control_plane/internal/transport/http.(*Server).handleGetAlertEvents-fm (3 handlers)
[GIN-debug] GET    /api/alerts/events/active --> aiops/control_plane/internal/transport/http.(*Server).handleGetActiveAlerts-fm (3 handlers)
[GIN-debug] POST   /api/alerts/events/:id/resolve --> aiops/control_plane/internal/transport/http.(*Server).handleResolveAlert-fm (3 handlers)
[GIN-debug] GET    /api/users                --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllUsers-fm (4 handlers)
[GIN-debug] POST   /api/users                --> aiops/control_plane/internal/transport/http.(*Server).handleCreateUser-fm (4 handlers)
[GIN-debug] GET    /api/users/:id            --> aiops/control_plane/internal/transport/http.(*Server).handleGetUser-fm (4 handlers)
[GIN-debug] PUT    /api/users/:id            --> aiops/control_plane/internal/transport/http.(*Server).handleUpdateUser-fm (4 handlers)
[GIN-debug] DELETE /api/users/:id            --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteUser-fm (4 handlers)
[GIN-debug] POST   /api/users/:id/password   --> aiops/control_plane/internal/transport/http.(*Server).handleChangePassword-fm (4 handlers)
[GIN-debug] GET    /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (2 handlers)
[GIN-debug] HEAD   /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (2 handlers)
[GIN-debug] GET    /                         --> aiops/control_plane/internal/transport/http.(*Server).setupRoutes.(*RouterGroup).StaticFile.func2 (2 handlers)
[GIN-debug] HEAD   /                         --> aiops/control_plane/internal/transport/http.(*Server).setupRoutes.(*RouterGroup).StaticFile.func2 (2 handlers)
2025-05-24 23:35:55.714227000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:79	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-24 23:35:55.714249000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:98	DevInsight Control Plane 已启动
2025-05-24 23:35:55.714258000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:99	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-24 23:35:55.714266000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:100	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-24 23:35:55.714274000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:101	按 Ctrl+C 退出
2025-05-24 23:36:00.421248000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:95	接收到 Agent 注册请求	{"agentID": "test-agent-001", "IP": "*************"}
2025-05-24 23:36:00.421279000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "test-agent-001", "IP": "*************", "支持的采集器类型": ["system", "mysql", "redis", "docker"]}
2025-05-24 23:36:00.422254000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:85	更新 Agent 信息	{"agentID": "test-agent-001"}
2025-05-24 23:36:00.422834000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:122	Agent 连接到任务流	{"agentID": "test-agent-001"}
2025-05-24 23:36:00.422952000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "test-agent-001"}
2025-05-24 23:36:00.423639000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "test-agent-001", "taskID": "test-agent-001", "状态": "connected"}
2025-05-24 23:36:10.425922000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	Agent 连接到日志数据流	{"agentID": "test-agent-001"}
2025-05-24 23:36:44.982970000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:109	正在关闭服务...
2025-05-24 23:36:44.983169000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:93	正在停止 HTTP API 服务器
2025-05-24 23:36:44.983394000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:102	HTTP API 服务器已停止
2025-05-24 23:36:44.983424000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:88	正在停止 gRPC 服务器
2025-05-24 23:36:44.983482000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:192	stream context 已完成	{"agentID": "test-agent-001"}
2025-05-24 23:36:44.983494000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:264
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:191
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-24 23:36:44.983448000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:221
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:184
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-24 23:36:44.983552000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155	接收任务状态失败	{"agentID": "test-agent-001", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:155
2025-05-24 23:36:44.983907000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:118	DevInsight Control Plane 已关闭
