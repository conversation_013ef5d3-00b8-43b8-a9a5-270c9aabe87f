load conf file: config/agent_config.yaml
2025-05-24 23:36:00.415161000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-24 23:36:00.415612000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "test-agent-001"}
2025-05-24 23:36:00.415632000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-24 23:36:00.415698000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-24 23:36:00.420656000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-24 23:36:00.420693000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-24 23:36:00.422463000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-24 23:36:00.422507000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-24 23:36:00.415698000"}
2025-05-24 23:36:00.422525000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-24 23:36:00.420655000"}
2025-05-24 23:36:00.422641000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-24 23:36:00.422592000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-24 23:36:00.422647000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-24 23:36:00.422679000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-24 23:36:00.422769000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-24 23:36:00.422844000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-24 23:36:00.422871000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-24 23:36:00.422906000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-24 23:36:00.422944000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-24 23:36:00.422952000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-24 23:36:10.425322000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-24 23:36:20.426724000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 6, "dropped": 0}
2025-05-24 23:36:44.982878000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:176	接收到退出信号，正在关闭 Agent...
2025-05-24 23:36:44.983173000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:338	指标流上下文取消，正在关闭...
2025-05-24 23:36:44.983190000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:398	日志流上下文取消，正在关闭...
2025-05-24 23:36:44.983247000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:284	任务流正在关闭...
2025-05-24 23:36:44.983277000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:286	所有采集器已停止.
2025-05-24 23:36:44.983269000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:251	停止接收任务配置，流已关闭或上下文已取消:	{"error": "rpc error: code = Canceled desc = context canceled"}
2025-05-24 23:36:50.432560000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 68, "dropped": 0}
2025-05-24 23:37:00.420907000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:225	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-24 23:37:00.421172000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-24 23:37:00.421202000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-24 23:37:00.421163000"}
2025-05-24 23:37:00.421476000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-24 23:37:10.422579000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 1}
2025-05-24 23:37:10.438885000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 69, "dropped": 0}
2025-05-24 23:37:11.429575000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-24 23:37:21.430952000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 2}
2025-05-24 23:37:23.432188000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-24 23:37:33.433492000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 4}
2025-05-24 23:37:37.434686000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-24 23:37:47.436023000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 8}
2025-05-24 23:37:55.437245000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-24 23:38:05.440263000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 16}
2025-05-24 23:38:20.452307000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 83, "dropped": 0}
2025-05-24 23:38:21.542817000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-24 23:38:21.545298000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Reconnecting", "new_state": "Connected"}
2025-05-24 23:38:21.545406000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:337	重连成功	{"server": "localhost:50051"}
2025-05-24 23:38:21.545419000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-24 23:38:21.545287000"}
2025-05-24 23:39:00.564825000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 88, "dropped": 0}
2025-05-24 23:39:20.569483000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 94, "dropped": 0}
2025-05-24 23:39:21.545156000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:225	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-24 23:39:21.545363000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-24 23:39:21.545348000"}
2025-05-24 23:39:21.545386000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-24 23:39:21.545738000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-24 23:39:31.546851000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 1}
2025-05-24 23:39:32.548039000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-24 23:39:42.549335000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 2}
2025-05-24 23:39:44.550465000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-24 23:39:54.551739000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 4}
2025-05-24 23:39:58.552892000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-24 23:40:08.573953000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 8}
2025-05-24 23:40:16.678695000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-24 23:40:26.679838000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 16}
2025-05-24 23:40:42.779901000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-24 23:40:52.782891000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 32}
2025-05-24 23:41:24.885549000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 7, "server": "localhost:50051"}
2025-05-24 23:41:35.244038000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 64}
2025-05-24 23:42:39.405710000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 8, "server": "localhost:50051"}
2025-05-24 23:42:50.094940000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 128}
2025-05-24 23:43:00.662694000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 100, "dropped": 0}
2025-05-24 23:44:58.320009000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 9, "server": "localhost:50051"}
2025-05-24 23:45:08.406784000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 256}
2025-05-24 23:47:20.732143000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 101, "dropped": 0}
2025-05-24 23:49:00.819960000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 108, "dropped": 0}
2025-05-24 23:49:24.519805000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 10, "server": "localhost:50051"}
2025-05-24 23:49:34.886957000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-24 23:53:40.881209000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 112, "dropped": 0}
2025-05-24 23:54:34.888504000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 11, "server": "localhost:50051"}
2025-05-24 23:54:45.247194000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-24 23:57:01.198706000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 116, "dropped": 0}
2025-05-24 23:58:01.208057000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 121, "dropped": 0}
2025-05-24 23:59:31.227541000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 125, "dropped": 0}
2025-05-24 23:59:45.328263000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 12, "server": "localhost:50051"}
2025-05-24 23:59:55.435835000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-25 00:00:00.543169000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:264	监控的日志文件已被删除	{"file": "/var/log/system.log"}
2025-05-25 00:00:00.543270000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:215	移除日志文件监控	{"file": "/var/log/system.log"}
2025-05-25 00:04:55.434739000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 13, "server": "localhost:50051"}
2025-05-25 00:05:05.551816000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
t/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 341, "dropped": 0}
2025-05-24 23:48:58.674432000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 348, "dropped": 0}
2025-05-24 23:50:58.322202000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 17, "server": "localhost:50051"}
2025-05-24 23:51:08.326791000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-24 23:53:48.866660000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 352, "dropped": 0}
2025-05-24 23:56:08.326457000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 18, "server": "localhost:50051"}
2025-05-24 23:56:18.331025000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-24 23:57:09.007067000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 356, "dropped": 0}
2025-05-24 23:57:59.031729000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 361, "dropped": 0}
2025-05-24 23:59:39.150356000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 365, "dropped": 0}
2025-05-25 00:00:06.027396000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:264	监控的日志文件已被删除	{"file": "/var/log/system.log"}
2025-05-25 00:00:06.028081000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:215	移除日志文件监控	{"file": "/var/log/system.log"}
2025-05-25 00:01:18.411792000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 19, "server": "localhost:50051"}
2025-05-25 00:01:28.413588000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-25 00:06:28.412732000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 20, "server": "localhost:50051"}
2025-05-25 00:06:38.415146000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
