version: '3.8'

services:
  control-plane:
    build:
      context: .
      target: control-plane
    container_name: devinsight-control-plane
    ports:
      - "8080:8080"  # HTTP API
      - "50051:50051" # gRPC
    environment:
      - DEVINSIGHT_HTTP_PORT=8080
      - DEVINSIGHT_GRPC_PORT=50051
      - DEVINSIGHT_DB_PATH=/app/data/devinsight.db
      - DEVINSIGHT_EMAIL_SENDER=${EMAIL_SENDER:-}
      - DEVINSIGHT_EMAIL_PASSWORD=${EMAIL_PASSWORD:-}
      - DEVINSIGHT_EMAIL_SMTP_HOST=${EMAIL_SMTP_HOST:-smtp.example.com}
      - DEVINSIGHT_EMAIL_SMTP_PORT=${EMAIL_SMTP_PORT:-587}
      - DEVINSIGHT_ALERT_THRESHOLD=${ALERT_THRESHOLD:-90}
    volumes:
      - devinsight_data:/app/data
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped

  agent:
    build:
      context: .
      target: agent
    container_name: devinsight-agent
    environment:
      - DEVINSIGHT_AGENT_ID=docker-agent-1
    command: ["--server", "control-plane:50051"]
    depends_on:
      - control-plane
    restart: unless-stopped

volumes:
  devinsight_data:
