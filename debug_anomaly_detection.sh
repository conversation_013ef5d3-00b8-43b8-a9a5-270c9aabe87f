#!/bin/bash

# 诊断异常检测问题
set -e

BASE_URL="http://localhost:8080"
TOKEN="dev-token"

echo "=== DevInsight 异常检测问题诊断 ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

DEVICE_ID="debug-anomaly-device"
BASE_TIME=1732545000

echo "=============================================="
log_info "步骤1: 检查插件缓冲区大小配置"
echo "=============================================="

# 获取插件配置信息
response=$(curl -s "${BASE_URL}/api/plugins?token=${TOKEN}")
echo "插件列表:"
echo "$response" | jq '.data.plugins[] | select(.type == "analyzer") | {name: .name, version: .version}'

echo
echo "=============================================="
log_info "步骤2: 累积足够的数据点（确保>10个）"
echo "=============================================="

# 为同一个设备累积大量数据
echo "为设备 $DEVICE_ID 累积60批数据（每批5个指标，总计300个数据点）..."

for i in {1..60}; do
    START_TIME=$((BASE_TIME + i * 60))  # 每分钟一批
    END_TIME=$((START_TIME + 60))
    
    response=$(curl -s -X POST "${BASE_URL}/api/plugins/analyze?token=${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"device_id\": \"${DEVICE_ID}\",
            \"start_time\": ${START_TIME},
            \"end_time\": ${END_TIME},
            \"limit\": 20
        }")
    
    # 每10次检查一下分析结果
    if [[ $((i % 10)) -eq 0 ]]; then
        log_info "已累积 $i 批数据，检查分析结果..."
        echo "$response" | jq '.data.analysis_results[] | {status: .status, message: .message, anomaly_count: (.anomalies | length)}'
        echo
    fi
done

log_success "数据累积完成，共60批数据"

echo
echo "=============================================="
log_info "步骤3: 使用极低阈值进行异常检测"
echo "=============================================="

# 使用极低阈值，应该能检测到异常
log_info "使用阈值 0.5（应该检测到大量异常）"
response=$(curl -s -X POST "${BASE_URL}/api/plugins/detect-anomalies?token=${TOKEN}" \
    -H "Content-Type: application/json" \
    -d "{
        \"device_id\": \"${DEVICE_ID}\",
        \"start_time\": $((BASE_TIME + 1000)),
        \"end_time\": $((BASE_TIME + 4000)),
        \"threshold\": 0.5
    }")

echo "异常检测结果 (阈值=0.5):"
echo "$response" | jq
anomaly_count=$(echo "$response" | jq '.data.anomaly_count // 0')
log_info "检测到的异常数量: $anomaly_count"

echo
echo "=============================================="
log_info "步骤4: 检查生成的测试数据分布"
echo "=============================================="

# 生成一组测试数据，检查其统计分布
log_info "生成100个测试数据点，分析其分布..."
test_response=$(curl -s -X POST "${BASE_URL}/api/plugins/analyze?token=${TOKEN}" \
    -H "Content-Type: application/json" \
    -d "{
        \"device_id\": \"data-analysis-device\",
        \"start_time\": ${BASE_TIME},
        \"end_time\": $((BASE_TIME + 6000)),
        \"limit\": 100
    }")

echo "生成的测试数据分析:"
echo "$test_response" | jq '.data | {total_metrics: .total_metrics, analysis_count: .analysis_count}'

echo
echo "=============================================="
log_info "步骤5: 直接分析simple-analyzer插件行为"
echo "=============================================="

# 用固定的数据模式测试异常检测
log_info "使用明显的异常数据进行测试..."

# 为一个新设备连续发送稳定数据，然后发送异常数据
STABLE_DEVICE="stable-test-device"

# 先发送20批稳定数据（值都在50左右）
for i in {1..20}; do
    START_TIME=$((BASE_TIME + 100 + i * 60))
    END_TIME=$((START_TIME + 60))
    
    curl -s -X POST "${BASE_URL}/api/plugins/analyze?token=${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"device_id\": \"${STABLE_DEVICE}\",
            \"start_time\": ${START_TIME},
            \"end_time\": ${END_TIME},
            \"limit\": 1
        }" > /dev/null
done

log_info "稳定基线数据发送完成"

# 再发送一些数据进行检测
response=$(curl -s -X POST "${BASE_URL}/api/plugins/detect-anomalies?token=${TOKEN}" \
    -H "Content-Type: application/json" \
    -d "{
        \"device_id\": \"${STABLE_DEVICE}\",
        \"start_time\": $((BASE_TIME + 1500)),
        \"end_time\": $((BASE_TIME + 2500)),
        \"threshold\": 1.0
    }")

echo "稳定设备的异常检测结果:"
echo "$response" | jq
stable_anomaly_count=$(echo "$response" | jq '.data.anomaly_count // 0')
log_info "稳定设备异常数量: $stable_anomaly_count"

echo
echo "=============================================="
log_info "步骤6: 检查插件内部统计信息"
echo "=============================================="

# 获取simple-analyzer插件的内部指标
log_info "获取simple-analyzer插件内部指标..."
metrics_response=$(curl -s "${BASE_URL}/api/plugins/simple-analyzer/1.0.0/metrics?token=${TOKEN}")
echo "Simple Analyzer内部指标:"
echo "$metrics_response" | jq

echo
echo "=============================================="
log_info "诊断总结"
echo "=============================================="

echo "1. 数据累积情况:"
echo "   - 主要测试设备: $DEVICE_ID (60批数据)"
echo "   - 稳定测试设备: $STABLE_DEVICE (20批稳定数据)"
echo

echo "2. 异常检测结果:"
echo "   - 主要设备异常数量 (阈值0.5): $anomaly_count"
echo "   - 稳定设备异常数量 (阈值1.0): $stable_anomaly_count"
echo

if [[ "$anomaly_count" -eq 0 && "$stable_anomaly_count" -eq 0 ]]; then
    log_error "问题确认：异常检测算法没有工作"
    echo
    echo "可能的原因:"
    echo "1. 测试数据的方差仍然在算法的正常范围内"
    echo "2. Z-score计算中标准差可能太大，导致异常值被认为是正常的"
    echo "3. 算法的窗口大小配置可能有问题"
    echo "4. 数据缓冲区可能没有正确累积历史数据"
    echo
    echo "建议解决方案:"
    echo "1. 降低默认阈值（从2.0改为1.0或0.5）"
    echo "2. 增加测试数据的异常值比例和极值程度"
    echo "3. 检查并调整窗口大小配置"
    echo "4. 添加调试日志查看实际的均值、标准差和Z-score计算"
else
    log_success "异常检测算法正在工作！"
    echo "检测到了 $((anomaly_count + stable_anomaly_count)) 个异常"
fi

echo
log_info "诊断脚本完成"
