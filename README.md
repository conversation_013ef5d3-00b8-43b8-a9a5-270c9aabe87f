# DevInsight - 设备监控与告警系统

DevInsight 是一个轻量级的设备监控与告警系统，专为中小型企业设计，用于监控各种 IT 基础设施设备，如服务器、数据库、网络设备等。系统采用分布式架构，由中央控制平面和可部署在各设备附近的采集代理组成。

## 核心特性

- **多种设备支持**：监控 MySQL、Redis、系统性能等
- **实时监控**：通过 gRPC 流式通信，实时采集与监控设备指标
- **灵活告警规则**：支持阈值告警和趋势分析
- **多渠道通知**：邮件、短信等多种告警通知方式
- **可扩展架构**：控制平面与采集代理分离，支持水平扩展
- **轻量级部署**：支持容器化部署，资源占用小

## 系统架构

DevInsight 系统由以下主要组件构成：

### 控制平面 (Control Plane)

控制平面是系统的核心，负责：
- 管理所有采集代理
- 存储与分析指标数据
- 处理告警规则与事件
- 提供 Web UI 和 REST API

### 采集代理 (Agent)

采集代理部署在需要监控的环境中，负责：
- 连接并监控各类设备
- 采集设备性能指标
- 向控制平面报告数据
- 执行来自控制平面的任务

### 通信协议

- 控制平面与代理之间的通信使用 gRPC 协议
- 控制平面对外提供 RESTful HTTP API
- 指标数据采用高效的二进制格式传输

## 部署方式

### 使用 Docker Compose

最简单的部署方式是使用 Docker Compose:

```bash
# 克隆代码库
git clone https://github.com/yourusername/devinsight.git
cd devinsight

# 配置环境变量（可选）
cp .env.example .env
# 编辑 .env 文件

# 启动服务
docker-compose up -d
```

### 手动构建与运行

```bash
# 构建控制平面
cd control_plane/cmd
go build -o devinsight-control-plane

# 运行控制平面
./devinsight-control-plane

# 构建代理
cd ../../agent/cmd
go build -o devinsight-agent

# 运行代理
./devinsight-agent --server localhost:50051
```

## 使用示例

### 添加一个设备监控

1. 访问 Web UI (http://localhost:8080)
2. 登录（默认用户名/密码: admin/admin123）
3. 导航至"设备管理"，点击"添加设备"
4. 填写设备信息并选择关联的代理
5. 提交后，系统会自动创建采集任务

### 设置告警规则

1. 导航至"告警管理"，点击"添加告警规则"
2. 选择设备和指标
3. 设置告警阈值和通知方式
4. 保存规则

## 开发指南

### 目录结构

```
devinsight/
├── agent/                 # 代理代码
│   ├── cmd/              # 命令行入口
│   └── internal/         # 内部实现
├── control_plane/        # 控制平面代码
│   ├── cmd/              # 命令行入口
│   ├── config/           # 配置管理
│   └── internal/         # 内部实现
│       ├── database/     # 数据库访问
│       ├── model/        # 数据模型
│       ├── repository/   # 数据访问层
│       ├── service/      # 业务逻辑层
│       ├── transport/    # 传输层(HTTP/gRPC)
│       └── util/         # 工具类
└── pkg/                  # 共享包
    └── proto/            # 协议定义
```

### 环境要求

- Go 1.24+
- SQLite3
- gRPC
- Docker & Docker Compose (可选)

## 配置选项

### 控制平面配置

控制平面通过环境变量或命令行参数进行配置：

- `DEVINSIGHT_HTTP_PORT`: HTTP API 端口 (默认: 8080)
- `DEVINSIGHT_GRPC_PORT`: gRPC 服务端口 (默认: 50051)
- `DEVINSIGHT_DB_PATH`: SQLite 数据库文件路径 (默认: ./devinsight.db)

### 代理配置

代理通过命令行参数或环境变量进行配置：

- `--server`: 控制平面服务器地址 (默认: localhost:50051)
- `--agent-id`: 代理 ID (默认: 自动生成)
- `DEVINSIGHT_AGENT_ID`: 代理 ID 环境变量

## 贡献指南

欢迎贡献代码、报告问题或提出新功能建议！

1. Fork 代码库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。
